"""
Main application orchestrator for AI Terminal.

Handles application lifecycle, dependency injection, and coordination
between different components of the system.
"""

import asyncio
import signal
import sys
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
import atexit

from ai_terminal.config.settings import get_settings, Settings
from ai_terminal.providers.base import BaseProvider
from ai_terminal.providers.openai_client import OpenAIProvider, DeepseekProvider
from ai_terminal.providers.anthropic_client import AnthropicProvider
from ai_terminal.providers.ollama_client import OllamaProvider
from ai_terminal.agents.agent_loop import AgentLoop
from ai_terminal.agents.tools import ToolRegistry
from ai_terminal.security.approval import ApprovalManager
from ai_terminal.storage.sessions import SessionManager
from ai_terminal.utils.logger import get_logger, setup_logging
from ai_terminal import __version__

logger = get_logger(__name__)


class AITerminalApp:
    """Main application class for AI Terminal."""
    
    def __init__(self):
        """Initialize the application."""
        self.settings: Optional[Settings] = None
        self.providers: Dict[str, BaseProvider] = {}
        self.current_provider: Optional[BaseProvider] = None
        self.agent_loop: Optional[AgentLoop] = None
        self.tool_registry: Optional[ToolRegistry] = None
        self.approval_manager: Optional[ApprovalManager] = None
        self.session_manager: Optional[SessionManager] = None
        
        # Application state
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        # Register cleanup on exit
        atexit.register(self._cleanup_sync)
    
    async def initialize(self, config_file: Optional[str] = None) -> None:
        """Initialize the application.
        
        Args:
            config_file: Optional path to configuration file
        """
        try:
            logger.info(f"Initializing AI Terminal v{__version__}")
            
            # Load settings
            self.settings = get_settings()
            if config_file:
                custom_settings = Settings.load_from_file(config_file)
                self.settings.__dict__.update(custom_settings.__dict__)
            
            # Setup logging with settings
            setup_logging(
                level=self.settings.logging.level,
                enable_file_logging=self.settings.logging.file_enabled,
                enable_console_logging=self.settings.logging.console_enabled,
            )
            
            # Initialize components
            await self._initialize_components()
            
            # Initialize providers
            await self._initialize_providers()
            
            logger.info("AI Terminal initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}", exc_info=True)
            raise
    
    async def _initialize_components(self) -> None:
        """Initialize core components."""
        # Tool registry
        self.tool_registry = ToolRegistry()
        logger.debug("Initialized tool registry")
        
        # Approval manager
        self.approval_manager = ApprovalManager(interactive=True)
        logger.debug("Initialized approval manager")
        
        # Session manager
        self.session_manager = SessionManager()
        logger.debug("Initialized session manager")
    
    async def _initialize_providers(self) -> None:
        """Initialize AI providers."""
        providers_to_init = []
        
        # OpenAI
        if self.settings.get_api_key("openai"):
            providers_to_init.append(("openai", OpenAIProvider, {
                "api_key": self.settings.get_api_key("openai"),
                "model": "gpt-4",
            }))
        
        # Deepseek
        if self.settings.get_api_key("deepseek"):
            providers_to_init.append(("deepseek", DeepseekProvider, {
                "api_key": self.settings.get_api_key("deepseek"),
            }))
        
        # Anthropic
        if self.settings.get_api_key("anthropic"):
            providers_to_init.append(("anthropic", AnthropicProvider, {
                "api_key": self.settings.get_api_key("anthropic"),
                "model": "claude-3-sonnet-20240229",
            }))
        
        # Ollama (always available if running)
        providers_to_init.append(("ollama", OllamaProvider, {}))
        
        # Initialize providers
        for name, provider_class, kwargs in providers_to_init:
            try:
                provider = provider_class(**kwargs)
                await provider.initialize()
                
                # Test connection
                if await provider.validate_connection():
                    self.providers[name] = provider
                    logger.info(f"Initialized provider: {name}")
                else:
                    logger.warning(f"Provider {name} failed connection test")
                    await provider.cleanup()
                    
            except Exception as e:
                logger.warning(f"Failed to initialize provider {name}: {e}")
        
        # Set default provider
        if self.settings.default_provider in self.providers:
            self.current_provider = self.providers[self.settings.default_provider]
        elif self.providers:
            # Use first available provider
            self.current_provider = next(iter(self.providers.values()))
            logger.info(f"Using fallback provider: {self.current_provider.name}")
        else:
            logger.error("No providers available")
            raise RuntimeError("No AI providers could be initialized")
    
    async def create_agent_loop(self, provider_name: Optional[str] = None) -> AgentLoop:
        """Create an agent loop instance.
        
        Args:
            provider_name: Name of provider to use
            
        Returns:
            AgentLoop instance
        """
        if provider_name and provider_name in self.providers:
            provider = self.providers[provider_name]
        else:
            provider = self.current_provider
        
        if not provider:
            raise RuntimeError("No provider available")
        
        agent_loop = AgentLoop(
            provider=provider,
            tool_registry=self.tool_registry,
            approval_manager=self.approval_manager,
            session_manager=self.session_manager,
        )
        
        return agent_loop
    
    async def switch_provider(self, provider_name: str) -> bool:
        """Switch to a different provider.
        
        Args:
            provider_name: Name of provider to switch to
            
        Returns:
            True if successful, False otherwise
        """
        if provider_name not in self.providers:
            logger.error(f"Provider not available: {provider_name}")
            return False
        
        self.current_provider = self.providers[provider_name]
        logger.info(f"Switched to provider: {provider_name}")
        return True
    
    def get_available_providers(self) -> Dict[str, BaseProvider]:
        """Get all available providers."""
        return self.providers.copy()
    
    def get_current_provider(self) -> Optional[BaseProvider]:
        """Get the current provider."""
        return self.current_provider
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run health check on all components.
        
        Returns:
            Health check results
        """
        results = {
            "status": "healthy",
            "version": __version__,
            "providers": {},
            "components": {},
        }
        
        # Check providers
        for name, provider in self.providers.items():
            try:
                is_healthy = await provider.validate_connection()
                results["providers"][name] = {
                    "status": "healthy" if is_healthy else "unhealthy",
                    "name": provider.name,
                }
            except Exception as e:
                results["providers"][name] = {
                    "status": "error",
                    "error": str(e),
                }
        
        # Check components
        results["components"]["tool_registry"] = {
            "status": "healthy",
            "tool_count": len(self.tool_registry.tools) if self.tool_registry else 0,
        }
        
        results["components"]["session_manager"] = {
            "status": "healthy",
            "sessions_dir": str(self.session_manager.sessions_dir) if self.session_manager else None,
        }
        
        # Overall status
        provider_healthy = any(
            p.get("status") == "healthy" 
            for p in results["providers"].values()
        )
        
        if not provider_healthy:
            results["status"] = "unhealthy"
        
        return results
    
    async def cleanup(self) -> None:
        """Cleanup application resources."""
        logger.info("Cleaning up AI Terminal")
        
        # Cleanup providers
        for name, provider in self.providers.items():
            try:
                await provider.cleanup()
                logger.debug(f"Cleaned up provider: {name}")
            except Exception as e:
                logger.warning(f"Error cleaning up provider {name}: {e}")
        
        self.providers.clear()
        self.current_provider = None
        
        # Reset components
        self.agent_loop = None
        self.tool_registry = None
        self.approval_manager = None
        self.session_manager = None
        
        logger.info("AI Terminal cleanup completed")
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        if sys.platform != "win32":
            # Unix-like systems
            for sig in [signal.SIGINT, signal.SIGTERM]:
                signal.signal(sig, self._signal_handler)
        else:
            # Windows
            signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating shutdown")
        self.shutdown_event.set()
    
    def _cleanup_sync(self):
        """Synchronous cleanup for atexit."""
        if self.providers:
            # Run cleanup in a new event loop if needed
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Create a new task for cleanup
                    asyncio.create_task(self.cleanup())
                else:
                    loop.run_until_complete(self.cleanup())
            except Exception as e:
                logger.warning(f"Error in sync cleanup: {e}")
    
    @asynccontextmanager
    async def lifespan(self):
        """Async context manager for application lifespan."""
        await self.initialize()
        try:
            yield self
        finally:
            await self.cleanup()


# Global application instance
_app: Optional[AITerminalApp] = None


async def get_app() -> AITerminalApp:
    """Get the global application instance."""
    global _app
    if _app is None:
        _app = AITerminalApp()
        await _app.initialize()
    return _app


async def create_app(config_file: Optional[str] = None) -> AITerminalApp:
    """Create and initialize a new application instance."""
    app = AITerminalApp()
    await app.initialize(config_file)
    return app


async def main():
    """Main entry point for running the application directly."""
    async with AITerminalApp().lifespan() as app:
        logger.info("AI Terminal is running")
        
        # Wait for shutdown signal
        await app.shutdown_event.wait()
        
        logger.info("Shutting down AI Terminal")


if __name__ == "__main__":
    asyncio.run(main())
