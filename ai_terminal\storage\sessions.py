"""
Session management for conversation persistence.

Handles saving, loading, and managing conversation sessions
with support for metadata and search capabilities.
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
import aiofiles

from ai_terminal.agents.context import ConversationContext
from ai_terminal.providers.base import Message, MessageRole
from ai_terminal.config.settings import get_settings
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class SessionManager:
    """Manages conversation sessions."""
    
    def __init__(self):
        """Initialize session manager."""
        self.settings = get_settings()
        self.sessions_dir = self.settings.data_dir / "sessions"
        self.sessions_dir.mkdir(parents=True, exist_ok=True)
        
        # In-memory cache for recent sessions
        self._session_cache: Dict[str, ConversationContext] = {}
        self._cache_max_size = 10
    
    async def save_session(self, context: ConversationContext) -> None:
        """Save a conversation session.
        
        Args:
            context: Conversation context to save
        """
        try:
            session_file = self.sessions_dir / f"{context.session_id}.json"
            
            # Convert context to serializable format
            session_data = {
                "session_id": context.session_id,
                "messages": [self._message_to_dict(msg) for msg in context.messages],
                "provider_name": context.provider.name if context.provider else None,
                "model": context.model,
                "temperature": context.temperature,
                "max_tokens": context.max_tokens,
                "system_message": context.system_message,
                "metadata": context.metadata,
                "created_at": context.created_at.isoformat(),
                "updated_at": context.updated_at.isoformat(),
            }
            
            # Write to file
            async with aiofiles.open(session_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(session_data, indent=2, ensure_ascii=False))
            
            # Update cache
            self._session_cache[context.session_id] = context
            self._cleanup_cache()
            
            logger.debug(f"Saved session: {context.session_id}")
            
        except Exception as e:
            logger.error(f"Failed to save session {context.session_id}: {e}")
            raise
    
    async def load_session(self, session_id: str) -> ConversationContext:
        """Load a conversation session.
        
        Args:
            session_id: Session ID to load
            
        Returns:
            ConversationContext for the session
            
        Raises:
            FileNotFoundError: If session doesn't exist
            ValueError: If session data is invalid
        """
        # Check cache first
        if session_id in self._session_cache:
            logger.debug(f"Loaded session from cache: {session_id}")
            return self._session_cache[session_id]
        
        try:
            session_file = self.sessions_dir / f"{session_id}.json"
            
            if not session_file.exists():
                raise FileNotFoundError(f"Session not found: {session_id}")
            
            # Read session data
            async with aiofiles.open(session_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                session_data = json.loads(content)
            
            # Convert back to ConversationContext
            context = ConversationContext(
                session_id=session_data["session_id"],
                messages=[self._dict_to_message(msg_dict) for msg_dict in session_data["messages"]],
                provider=None,  # Will be set by caller
                model=session_data.get("model"),
                temperature=session_data.get("temperature", 0.7),
                max_tokens=session_data.get("max_tokens"),
                system_message=session_data.get("system_message"),
                metadata=session_data.get("metadata", {}),
                created_at=datetime.fromisoformat(session_data["created_at"]),
                updated_at=datetime.fromisoformat(session_data["updated_at"]),
            )
            
            # Update cache
            self._session_cache[session_id] = context
            self._cleanup_cache()
            
            logger.debug(f"Loaded session: {session_id}")
            return context
            
        except Exception as e:
            logger.error(f"Failed to load session {session_id}: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data as dictionary.

        Args:
            session_id: Session ID to get

        Returns:
            Session data dictionary or None if not found
        """
        try:
            session_file = self.sessions_dir / f"{session_id}.json"

            if not session_file.exists():
                return None

            # Read session data
            async with aiofiles.open(session_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                session_data = json.loads(content)

            return session_data

        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None

    async def delete_session(self, session_id: str) -> bool:
        """Delete a conversation session.

        Args:
            session_id: Session ID to delete

        Returns:
            True if deleted, False if not found
        """
        try:
            session_file = self.sessions_dir / f"{session_id}.json"

            if session_file.exists():
                session_file.unlink()

                # Remove from cache
                self._session_cache.pop(session_id, None)

                logger.info(f"Deleted session: {session_id}")
                return True
            else:
                logger.warning(f"Session not found for deletion: {session_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    async def list_sessions(
        self,
        limit: Optional[int] = None,
        offset: int = 0,
        sort_by: str = "updated_at",
        reverse: bool = True
    ) -> List[Dict[str, Any]]:
        """List conversation sessions.
        
        Args:
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            sort_by: Field to sort by (created_at, updated_at, session_id)
            reverse: Sort in reverse order
            
        Returns:
            List of session metadata
        """
        try:
            sessions = []
            
            # Get all session files
            session_files = list(self.sessions_dir.glob("*.json"))
            
            for session_file in session_files:
                try:
                    async with aiofiles.open(session_file, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        session_data = json.loads(content)
                    
                    # Extract metadata
                    metadata = {
                        "session_id": session_data["session_id"],
                        "message_count": len(session_data.get("messages", [])),
                        "provider_name": session_data.get("provider_name"),
                        "model": session_data.get("model"),
                        "created_at": session_data["created_at"],
                        "updated_at": session_data["updated_at"],
                        "system_message": session_data.get("system_message", "")[:100],  # Truncated
                    }
                    
                    # Add first user message as preview
                    messages = session_data.get("messages", [])
                    user_messages = [msg for msg in messages if msg.get("role") == "user"]
                    if user_messages:
                        first_message = user_messages[0].get("content", "")
                        metadata["preview"] = first_message[:200] + "..." if len(first_message) > 200 else first_message
                    else:
                        metadata["preview"] = "No messages"
                    
                    sessions.append(metadata)
                    
                except Exception as e:
                    logger.warning(f"Failed to read session file {session_file}: {e}")
                    continue
            
            # Sort sessions
            if sort_by in ["created_at", "updated_at"]:
                sessions.sort(
                    key=lambda s: datetime.fromisoformat(s[sort_by]),
                    reverse=reverse
                )
            else:
                sessions.sort(key=lambda s: s.get(sort_by, ""), reverse=reverse)
            
            # Apply pagination
            if offset > 0:
                sessions = sessions[offset:]
            if limit is not None:
                sessions = sessions[:limit]
            
            return sessions
            
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            return []
    
    async def search_sessions(
        self,
        query: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Search sessions by content.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching session metadata
        """
        try:
            matching_sessions = []
            query_lower = query.lower()
            
            # Get all sessions
            all_sessions = await self.list_sessions()
            
            for session_meta in all_sessions:
                session_id = session_meta["session_id"]
                
                try:
                    # Load full session to search content
                    context = await self.load_session(session_id)
                    
                    # Search in messages
                    for message in context.messages:
                        if query_lower in message.content.lower():
                            # Add match info
                            session_meta["match_type"] = "message_content"
                            session_meta["match_preview"] = self._extract_match_preview(
                                message.content, query, 100
                            )
                            matching_sessions.append(session_meta)
                            break
                    
                    # Search in system message
                    if (context.system_message and 
                        query_lower in context.system_message.lower() and
                        session_meta not in matching_sessions):
                        session_meta["match_type"] = "system_message"
                        session_meta["match_preview"] = self._extract_match_preview(
                            context.system_message, query, 100
                        )
                        matching_sessions.append(session_meta)
                
                except Exception as e:
                    logger.warning(f"Failed to search session {session_id}: {e}")
                    continue
            
            # Sort by relevance (could be improved with proper scoring)
            matching_sessions.sort(
                key=lambda s: datetime.fromisoformat(s["updated_at"]),
                reverse=True
            )
            
            if limit is not None:
                matching_sessions = matching_sessions[:limit]
            
            return matching_sessions
            
        except Exception as e:
            logger.error(f"Failed to search sessions: {e}")
            return []
    
    async def cleanup_old_sessions(self, days: int = 30) -> int:
        """Clean up old sessions.
        
        Args:
            days: Delete sessions older than this many days
            
        Returns:
            Number of sessions deleted
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            sessions = await self.list_sessions()
            
            for session_meta in sessions:
                updated_at = datetime.fromisoformat(session_meta["updated_at"])
                
                if updated_at < cutoff_date:
                    if await self.delete_session(session_meta["session_id"]):
                        deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old sessions")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {e}")
            return 0
    
    def _message_to_dict(self, message: Message) -> Dict[str, Any]:
        """Convert Message to dictionary."""
        return {
            "role": message.role.value,
            "content": message.content,
            "name": message.name,
            "function_call": message.function_call,
            "tool_calls": message.tool_calls,
            "tool_call_id": message.tool_call_id,
            "timestamp": message.timestamp.isoformat() if message.timestamp else None,
        }
    
    def _dict_to_message(self, data: Dict[str, Any]) -> Message:
        """Convert dictionary to Message."""
        return Message(
            role=MessageRole(data["role"]),
            content=data["content"],
            name=data.get("name"),
            function_call=data.get("function_call"),
            tool_calls=data.get("tool_calls"),
            tool_call_id=data.get("tool_call_id"),
            timestamp=datetime.fromisoformat(data["timestamp"]) if data.get("timestamp") else None,
        )
    
    def _extract_match_preview(self, text: str, query: str, max_length: int) -> str:
        """Extract a preview around the matching query."""
        query_lower = query.lower()
        text_lower = text.lower()
        
        match_index = text_lower.find(query_lower)
        if match_index == -1:
            return text[:max_length]
        
        # Calculate preview window
        start = max(0, match_index - max_length // 2)
        end = min(len(text), start + max_length)
        
        preview = text[start:end]
        
        # Add ellipsis if truncated
        if start > 0:
            preview = "..." + preview
        if end < len(text):
            preview = preview + "..."
        
        return preview
    
    def _cleanup_cache(self):
        """Clean up session cache if it gets too large."""
        if len(self._session_cache) > self._cache_max_size:
            # Remove oldest entries
            sorted_items = sorted(
                self._session_cache.items(),
                key=lambda x: x[1].updated_at
            )
            
            # Keep only the most recent sessions
            self._session_cache = dict(sorted_items[-self._cache_max_size:])
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics about sessions.
        
        Returns:
            Dictionary with session statistics
        """
        try:
            sessions = await self.list_sessions()
            
            if not sessions:
                return {
                    "total_sessions": 0,
                    "total_messages": 0,
                    "providers": {},
                    "models": {},
                    "oldest_session": None,
                    "newest_session": None,
                }
            
            total_messages = sum(s["message_count"] for s in sessions)
            
            # Count by provider and model
            providers = {}
            models = {}
            
            for session in sessions:
                provider = session.get("provider_name", "unknown")
                model = session.get("model", "unknown")
                
                providers[provider] = providers.get(provider, 0) + 1
                models[model] = models.get(model, 0) + 1
            
            # Find oldest and newest
            oldest = min(sessions, key=lambda s: s["created_at"])
            newest = max(sessions, key=lambda s: s["updated_at"])
            
            return {
                "total_sessions": len(sessions),
                "total_messages": total_messages,
                "providers": providers,
                "models": models,
                "oldest_session": oldest["created_at"],
                "newest_session": newest["updated_at"],
            }
            
        except Exception as e:
            logger.error(f"Failed to get session stats: {e}")
            return {}
