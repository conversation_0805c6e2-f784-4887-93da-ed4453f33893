# 🧠 AI Terminal - Intelligent WSL Installation Guide v2.0

This guide provides a **smart, fully automatic script** that intelligently installs and updates AI Terminal in your Windows 11 WSL environment. The script only updates what's needed, preserving your configuration and avoiding unnecessary reinstallations.

## ✨ New Intelligent Features

### 🧠 **Smart Detection**
- **Detects existing installations** and only updates what's changed
- **Preserves your configuration** during updates
- **Tracks component versions** to avoid unnecessary reinstalls
- **Checks file timestamps** to detect when project needs updating

### 🎯 **Multiple Operation Modes**
- **Smart Install/Update** (default) - Only installs/updates what's needed
- **Force Reinstall** - Complete fresh installation
- **Update Only** - Updates components without running setup wizard
- **Setup Only** - Runs configuration wizard only
- **Status Check** - Shows what needs updating

### 💾 **State Management**
- **Remembers what's installed** in `~/.ai-terminal-install-state`
- **Tracks versions** of Python, Poetry, system packages, and project
- **Preserves API keys** and configuration between updates

## 📋 Prerequisites

1. **WSL2 installed** on Windows 11
2. **Ubuntu, Debian, or similar** Linux distribution in WSL
3. **Internet connection** for downloading packages
4. **AI Terminal project** exists in Windows at the specified path

## 🚀 Installation & Usage

### Step 1: Copy the Enhanced Script to WSL

```bash
# Navigate to your home directory
cd ~

# Copy the enhanced installation script from Windows
cp "/mnt/c/Users/<USER>/OneDrive/Documents/New folder/auto_install_wsl.sh" ./

# Make it executable
chmod +x auto_install_wsl.sh
```

### Step 2: Choose Your Installation Mode

#### 🎯 **Smart Install/Update (Recommended)**
```bash
./auto_install_wsl.sh
```
- Detects what's already installed
- Only updates what's needed
- Preserves existing configuration
- Runs setup wizard only if needed

#### 🔄 **Check What Needs Updating**
```bash
./auto_install_wsl.sh --check
```
- Shows current status of all components
- Tells you what would be updated
- No changes made

#### 🔧 **Update Components Only**
```bash
./auto_install_wsl.sh --update
```
- Updates system packages, Python, Poetry, project files
- Skips setup wizard
- Preserves existing configuration

#### ⚡ **Force Complete Reinstall**
```bash
./auto_install_wsl.sh --force
```
- Removes all state tracking
- Reinstalls everything from scratch
- Runs full setup wizard

#### 🎛️ **Setup Wizard Only**
```bash
./auto_install_wsl.sh --setup-only
```
- Runs only the configuration wizard
- Useful for changing API keys or providers
- Requires existing installation

#### 📖 **Help & Version**
```bash
./auto_install_wsl.sh --help     # Show all options
./auto_install_wsl.sh --version  # Show script version
```

## 🎮 What You'll Get

After installation, you'll have these convenient commands:

### 🚀 Quick Commands
- `ait` - Activate AI Terminal environment
- `ai-chat` - Start interactive chat
- `ai-quick "message"` - Send quick message
- `ai-health` - Check system health
- `ai-config` - Show configuration
- `ai-setup` - Re-run setup wizard

### 🔧 Installer Commands
- `./auto_install_wsl.sh --check` - Check what needs updating
- `./auto_install_wsl.sh --update` - Update components only
- `./auto_install_wsl.sh --setup-only` - Run setup wizard only
- `./auto_install_wsl.sh --force` - Force complete reinstall

## 🧠 How Smart Detection Works

### 📊 **State Tracking**
The script maintains a state file at `~/.ai-terminal-install-state` that tracks:
```
system_packages=2025-05-31|2025-05-31 12:00:00
python=3.11.5|2025-05-31 12:01:00
poetry=1.8.2|2025-05-31 12:02:00
system_deps=1.0|2025-05-31 12:03:00
project=2025-05-31 12:05:00|2025-05-31 12:05:00
```

### 🔍 **Smart Checks**
- **System Packages**: Only updates once per day
- **Python**: Checks if correct version is installed
- **Poetry**: Verifies installation and version
- **Project Files**: Compares timestamps between Windows and WSL
- **Dependencies**: Validates Poetry lock file integrity
- **Configuration**: Preserves existing API keys and settings

### 🔄 **Update Logic**
1. **First Run**: Full installation with setup wizard
2. **Subsequent Runs**: Only updates changed components
3. **Project Updates**: Backs up config, updates files, restores config
4. **Dependency Updates**: Only if lock file is invalid
5. **System Updates**: Only once per day unless forced

## 🛡️ Safety Features

### 💾 **Configuration Backup**
- Automatically backs up your AI Terminal configuration during updates
- Restores API keys and settings after project updates
- Never loses your setup

### 🔒 **Safe Execution**
- Won't run as root for security
- Comprehensive error handling with line numbers
- Graceful fallbacks for different installation methods

### 🎯 **Selective Updates**
- Only touches what actually needs updating
- Preserves working installations
- Minimal disruption to your workflow

## 🎯 Example Workflows

### 🆕 **First Time Installation**
```bash
./auto_install_wsl.sh
# Full installation + setup wizard
```

### 🔄 **Daily Usage**
```bash
./auto_install_wsl.sh
# Output: "All components are up to date!"
# Takes 2 seconds instead of 10 minutes
```

### 📦 **After Project Updates**
```bash
./auto_install_wsl.sh
# Detects newer files in Windows
# Updates project while preserving config
```

### 🔧 **Changing API Keys**
```bash
./auto_install_wsl.sh --setup-only
# Quick reconfiguration without reinstalling
```

### 🚨 **Troubleshooting**
```bash
./auto_install_wsl.sh --force
# Nuclear option - fresh start
```

## 🎉 Benefits of Enhanced Version

✅ **10x Faster Updates** - Only updates what's changed  
✅ **Configuration Preservation** - Never lose your API keys  
✅ **Intelligent Detection** - Knows what's already installed  
✅ **Multiple Modes** - Choose the right operation for your needs  
✅ **State Tracking** - Remembers installation history  
✅ **Safe Updates** - Backs up config before changes  
✅ **Minimal Disruption** - Quick updates without full reinstalls  
✅ **Flexible Usage** - From quick checks to complete reinstalls  

The enhanced script transforms from a "sledgehammer" approach to a "precision tool" that respects your existing setup while keeping everything up to date! 🎯
