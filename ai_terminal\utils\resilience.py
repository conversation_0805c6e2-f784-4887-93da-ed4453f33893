"""
Advanced error handling and resilience utilities.

Provides retry logic with exponential backoff, graceful degradation,
fallback mechanisms, and comprehensive error recovery.
"""

import asyncio
import random
import time
from typing import Any, Callable, Optional, Dict, List, Union, TypeVar, Awaitable
from functools import wraps
from dataclasses import dataclass
from enum import Enum
import traceback

from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


class RetryStrategy(str, Enum):
    """Retry strategy types."""
    FIXED = "fixed"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"
    EXPONENTIAL_JITTER = "exponential_jitter"


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_JITTER
    backoff_multiplier: float = 2.0
    jitter_range: float = 0.1
    exceptions: tuple = (Exception,)
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        if self.strategy == RetryStrategy.FIXED:
            delay = self.base_delay
        elif self.strategy == RetryStrategy.LINEAR:
            delay = self.base_delay * attempt
        elif self.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.base_delay * (self.backoff_multiplier ** (attempt - 1))
        elif self.strategy == RetryStrategy.EXPONENTIAL_JITTER:
            base_delay = self.base_delay * (self.backoff_multiplier ** (attempt - 1))
            jitter = random.uniform(-self.jitter_range, self.jitter_range) * base_delay
            delay = base_delay + jitter
        else:
            delay = self.base_delay
        
        return min(delay, self.max_delay)


class CircuitBreakerState(str, Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    expected_exception: tuple = (Exception,)


class CircuitBreaker:
    """Circuit breaker for preventing cascading failures."""
    
    def __init__(self, config: CircuitBreakerConfig):
        """Initialize circuit breaker."""
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.success_count = 0
    
    def can_execute(self) -> bool:
        """Check if execution is allowed."""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if time.time() - self.last_failure_time >= self.config.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                return True
            return False
        elif self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self):
        """Record a successful execution."""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= 3:  # Require multiple successes
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = max(0, self.failure_count - 1)
    
    def record_failure(self):
        """Record a failed execution."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN
        elif self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.OPEN


class ResilienceManager:
    """Manager for resilience patterns and error handling."""
    
    def __init__(self):
        """Initialize resilience manager."""
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        self.error_stats: Dict[str, Dict[str, Any]] = {}
    
    def retry(
        self,
        config: Optional[RetryConfig] = None,
        operation_name: Optional[str] = None
    ):
        """Decorator for adding retry logic to functions.
        
        Args:
            config: Retry configuration
            operation_name: Name for logging and stats
        """
        if config is None:
            config = RetryConfig()
        
        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                name = operation_name or func.__name__
                last_exception = None
                
                for attempt in range(1, config.max_attempts + 1):
                    try:
                        result = await func(*args, **kwargs)
                        
                        # Record success
                        self._record_success(name)
                        
                        if attempt > 1:
                            logger.info(f"Operation {name} succeeded on attempt {attempt}")
                        
                        return result
                        
                    except config.exceptions as e:
                        last_exception = e
                        self._record_failure(name, e, attempt)
                        
                        if attempt < config.max_attempts:
                            delay = config.calculate_delay(attempt)
                            logger.warning(
                                f"Operation {name} failed on attempt {attempt}/{config.max_attempts}, "
                                f"retrying in {delay:.2f}s: {e}"
                            )
                            await asyncio.sleep(delay)
                        else:
                            logger.error(
                                f"Operation {name} failed after {config.max_attempts} attempts: {e}"
                            )
                
                # All attempts failed
                raise last_exception
            
            return wrapper
        return decorator
    
    def circuit_breaker(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ):
        """Decorator for adding circuit breaker pattern.
        
        Args:
            name: Circuit breaker name
            config: Circuit breaker configuration
        """
        if config is None:
            config = CircuitBreakerConfig()
        
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(config)
        
        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                breaker = self.circuit_breakers[name]
                
                if not breaker.can_execute():
                    raise Exception(f"Circuit breaker {name} is open")
                
                try:
                    result = await func(*args, **kwargs)
                    breaker.record_success()
                    return result
                    
                except config.expected_exception as e:
                    breaker.record_failure()
                    raise
            
            return wrapper
        return decorator
    
    def fallback(
        self,
        fallback_func: Callable[..., Awaitable[T]],
        exceptions: tuple = (Exception,)
    ):
        """Decorator for adding fallback behavior.
        
        Args:
            fallback_func: Function to call on failure
            exceptions: Exceptions that trigger fallback
        """
        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    logger.warning(f"Primary function {func.__name__} failed, using fallback: {e}")
                    return await fallback_func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def timeout(self, seconds: float):
        """Decorator for adding timeout to functions.
        
        Args:
            seconds: Timeout in seconds
        """
        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                try:
                    return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
                except asyncio.TimeoutError:
                    logger.error(f"Function {func.__name__} timed out after {seconds}s")
                    raise
            
            return wrapper
        return decorator
    
    def graceful_degradation(
        self,
        degraded_func: Callable[..., Awaitable[T]],
        health_check: Optional[Callable[[], Awaitable[bool]]] = None
    ):
        """Decorator for graceful degradation.
        
        Args:
            degraded_func: Function to use when degraded
            health_check: Function to check if degradation is needed
        """
        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                # Check if we should degrade
                if health_check:
                    try:
                        is_healthy = await health_check()
                        if not is_healthy:
                            logger.info(f"Using degraded version of {func.__name__}")
                            return await degraded_func(*args, **kwargs)
                    except Exception as e:
                        logger.warning(f"Health check failed for {func.__name__}: {e}")
                        return await degraded_func(*args, **kwargs)
                
                # Try primary function
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"Primary function {func.__name__} failed, degrading: {e}")
                    return await degraded_func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def _record_success(self, operation_name: str):
        """Record a successful operation."""
        if operation_name not in self.error_stats:
            self.error_stats[operation_name] = {
                "total_attempts": 0,
                "successes": 0,
                "failures": 0,
                "last_success": None,
                "last_failure": None,
            }
        
        stats = self.error_stats[operation_name]
        stats["total_attempts"] += 1
        stats["successes"] += 1
        stats["last_success"] = time.time()
    
    def _record_failure(self, operation_name: str, exception: Exception, attempt: int):
        """Record a failed operation."""
        if operation_name not in self.error_stats:
            self.error_stats[operation_name] = {
                "total_attempts": 0,
                "successes": 0,
                "failures": 0,
                "last_success": None,
                "last_failure": None,
                "recent_errors": [],
            }
        
        stats = self.error_stats[operation_name]
        stats["total_attempts"] += 1
        stats["failures"] += 1
        stats["last_failure"] = time.time()
        
        # Keep recent errors for analysis
        error_info = {
            "timestamp": time.time(),
            "attempt": attempt,
            "exception_type": type(exception).__name__,
            "message": str(exception),
            "traceback": traceback.format_exc(),
        }
        
        if "recent_errors" not in stats:
            stats["recent_errors"] = []
        
        stats["recent_errors"].append(error_info)
        
        # Keep only recent errors (last 10)
        stats["recent_errors"] = stats["recent_errors"][-10:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get resilience statistics."""
        return {
            "operations": self.error_stats.copy(),
            "circuit_breakers": {
                name: {
                    "state": breaker.state.value,
                    "failure_count": breaker.failure_count,
                    "last_failure_time": breaker.last_failure_time,
                }
                for name, breaker in self.circuit_breakers.items()
            },
        }
    
    def reset_stats(self):
        """Reset all statistics."""
        self.error_stats.clear()
        for breaker in self.circuit_breakers.values():
            breaker.failure_count = 0
            breaker.state = CircuitBreakerState.CLOSED
            breaker.last_failure_time = 0.0


# Global resilience manager instance
resilience = ResilienceManager()


# Convenience decorators using global instance
def retry_on_failure(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_JITTER,
    exceptions: tuple = (Exception,)
):
    """Convenience decorator for retry logic."""
    config = RetryConfig(
        max_attempts=max_attempts,
        base_delay=base_delay,
        strategy=strategy,
        exceptions=exceptions
    )
    return resilience.retry(config)


def with_circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0
):
    """Convenience decorator for circuit breaker."""
    config = CircuitBreakerConfig(
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout
    )
    return resilience.circuit_breaker(name, config)


def with_timeout(seconds: float):
    """Convenience decorator for timeout."""
    return resilience.timeout(seconds)


def with_fallback(fallback_func: Callable, exceptions: tuple = (Exception,)):
    """Convenience decorator for fallback."""
    return resilience.fallback(fallback_func, exceptions)
