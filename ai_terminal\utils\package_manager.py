"""
Package manager integration and update checking.

Provides automatic detection of package managers, update checking
against PyPI, and version management capabilities.
"""

import asyncio
import json
import subprocess
import sys
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import aiohttp
from packaging import version

from ai_terminal.utils.logger import get_logger
from ai_terminal import __version__

logger = get_logger(__name__)


class PackageManager:
    """Package manager integration and update checker."""
    
    def __init__(self):
        """Initialize package manager."""
        self.current_version = __version__
        self.package_name = "ai-terminal"
        
        # Detected package managers
        self.managers = {}
        self._detect_package_managers()
    
    def _detect_package_managers(self):
        """Detect available package managers."""
        managers_to_check = [
            ("poetry", ["poetry", "--version"]),
            ("pip", [sys.executable, "-m", "pip", "--version"]),
            ("conda", ["conda", "--version"]),
            ("pipenv", ["pipenv", "--version"]),
        ]
        
        for name, command in managers_to_check:
            try:
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    self.managers[name] = {
                        "available": True,
                        "version": result.stdout.strip(),
                        "command": command[0],
                    }
                    logger.debug(f"Detected {name}: {result.stdout.strip()}")
                else:
                    self.managers[name] = {"available": False}
                    
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.managers[name] = {"available": False}
    
    async def check_for_updates(self) -> Dict[str, Any]:
        """Check for updates on PyPI.
        
        Returns:
            Update information dictionary
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"https://pypi.org/pypi/{self.package_name}/json"
                
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        latest_version = data["info"]["version"]
                        current_ver = version.parse(self.current_version)
                        latest_ver = version.parse(latest_version)
                        
                        update_available = latest_ver > current_ver
                        
                        return {
                            "update_available": update_available,
                            "current_version": self.current_version,
                            "latest_version": latest_version,
                            "package_info": {
                                "name": data["info"]["name"],
                                "summary": data["info"]["summary"],
                                "author": data["info"]["author"],
                                "home_page": data["info"]["home_page"],
                            },
                            "release_info": data["releases"].get(latest_version, []),
                        }
                    else:
                        return {
                            "error": f"Failed to check PyPI: HTTP {response.status}",
                            "update_available": False,
                            "current_version": self.current_version,
                        }
                        
        except asyncio.TimeoutError:
            return {
                "error": "Timeout checking for updates",
                "update_available": False,
                "current_version": self.current_version,
            }
        except Exception as e:
            logger.error(f"Failed to check for updates: {e}")
            return {
                "error": str(e),
                "update_available": False,
                "current_version": self.current_version,
            }
    
    async def get_package_info(self, package_name: str) -> Dict[str, Any]:
        """Get information about a package from PyPI.
        
        Args:
            package_name: Name of package to look up
            
        Returns:
            Package information
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"https://pypi.org/pypi/{package_name}/json"
                
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            "found": True,
                            "name": data["info"]["name"],
                            "version": data["info"]["version"],
                            "summary": data["info"]["summary"],
                            "description": data["info"]["description"],
                            "author": data["info"]["author"],
                            "license": data["info"]["license"],
                            "home_page": data["info"]["home_page"],
                            "requires_python": data["info"]["requires_python"],
                            "dependencies": data["info"].get("requires_dist", []),
                        }
                    else:
                        return {
                            "found": False,
                            "error": f"Package not found: HTTP {response.status}",
                        }
                        
        except Exception as e:
            return {
                "found": False,
                "error": str(e),
            }
    
    async def install_package(
        self,
        package_name: str,
        manager: str = "auto",
        version_spec: Optional[str] = None
    ) -> Dict[str, Any]:
        """Install a package using the specified manager.
        
        Args:
            package_name: Name of package to install
            manager: Package manager to use ("auto", "pip", "poetry", etc.)
            version_spec: Version specification (e.g., ">=1.0.0")
            
        Returns:
            Installation result
        """
        if manager == "auto":
            manager = self._get_preferred_manager()
        
        if manager not in self.managers or not self.managers[manager]["available"]:
            return {
                "success": False,
                "error": f"Package manager {manager} not available",
            }
        
        # Build package specification
        package_spec = package_name
        if version_spec:
            package_spec += version_spec
        
        try:
            if manager == "poetry":
                command = ["poetry", "add", package_spec]
            elif manager == "pip":
                command = [sys.executable, "-m", "pip", "install", package_spec]
            elif manager == "conda":
                command = ["conda", "install", "-y", package_spec]
            elif manager == "pipenv":
                command = ["pipenv", "install", package_spec]
            else:
                return {
                    "success": False,
                    "error": f"Unsupported package manager: {manager}",
                }
            
            # Execute installation
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "manager": manager,
                "package": package_spec,
                "stdout": stdout.decode('utf-8', errors='replace'),
                "stderr": stderr.decode('utf-8', errors='replace'),
                "return_code": process.returncode,
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "manager": manager,
                "package": package_spec,
            }
    
    async def update_self(self, manager: str = "auto") -> Dict[str, Any]:
        """Update AI Terminal to the latest version.
        
        Args:
            manager: Package manager to use
            
        Returns:
            Update result
        """
        # Check if update is available first
        update_info = await self.check_for_updates()
        
        if not update_info.get("update_available", False):
            return {
                "success": True,
                "message": "Already up to date",
                "current_version": self.current_version,
                "latest_version": update_info.get("latest_version", self.current_version),
            }
        
        # Perform update
        result = await self.install_package(
            self.package_name,
            manager=manager,
            version_spec=f"=={update_info['latest_version']}"
        )
        
        if result["success"]:
            result["message"] = f"Updated from {self.current_version} to {update_info['latest_version']}"
            result["restart_required"] = True
        
        return result
    
    def _get_preferred_manager(self) -> str:
        """Get the preferred package manager.
        
        Returns:
            Name of preferred manager
        """
        # Preference order
        preferences = ["poetry", "pip", "conda", "pipenv"]
        
        for manager in preferences:
            if self.managers.get(manager, {}).get("available", False):
                return manager
        
        return "pip"  # Fallback
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get information about the current environment.
        
        Returns:
            Environment information
        """
        try:
            # Python version
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            
            # Virtual environment detection
            in_venv = hasattr(sys, 'real_prefix') or (
                hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
            )
            
            # Get pip list if available
            installed_packages = {}
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "list", "--format=json"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    packages = json.loads(result.stdout)
                    installed_packages = {pkg["name"]: pkg["version"] for pkg in packages}
                    
            except Exception:
                pass
            
            return {
                "python_version": python_version,
                "python_executable": sys.executable,
                "virtual_environment": in_venv,
                "package_managers": self.managers,
                "installed_packages": installed_packages,
                "ai_terminal_version": self.current_version,
            }
            
        except Exception as e:
            logger.error(f"Failed to get environment info: {e}")
            return {
                "error": str(e),
                "ai_terminal_version": self.current_version,
            }
    
    async def check_dependencies(self) -> Dict[str, Any]:
        """Check if all required dependencies are installed.
        
        Returns:
            Dependency check results
        """
        try:
            # Required dependencies (from pyproject.toml)
            required_deps = [
                "typer", "pydantic", "pydantic-settings", "rich", "textual",
                "openai", "anthropic", "aiohttp", "aiofiles", "PyYAML",
                "watchdog", "psutil", "click", "plyer"
            ]
            
            # Optional dependencies
            optional_deps = ["win10toast"]  # Windows only
            
            results = {
                "all_satisfied": True,
                "required": {},
                "optional": {},
                "missing": [],
                "outdated": [],
            }
            
            # Check required dependencies
            for dep in required_deps:
                try:
                    __import__(dep.replace("-", "_"))
                    results["required"][dep] = {"installed": True}
                except ImportError:
                    results["required"][dep] = {"installed": False}
                    results["missing"].append(dep)
                    results["all_satisfied"] = False
            
            # Check optional dependencies
            for dep in optional_deps:
                try:
                    __import__(dep.replace("-", "_"))
                    results["optional"][dep] = {"installed": True}
                except ImportError:
                    results["optional"][dep] = {"installed": False}
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to check dependencies: {e}")
            return {
                "error": str(e),
                "all_satisfied": False,
            }
