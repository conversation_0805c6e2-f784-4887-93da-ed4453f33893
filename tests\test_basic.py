"""
Basic tests for AI Terminal functionality.

Tests core components and ensures the application can be imported
and initialized properly.
"""

import pytest
import asyncio
from pathlib import Path
import tempfile
import os

from ai_terminal.config.settings import Settings, get_settings
from ai_terminal.providers.base import Message, MessageRole
from ai_terminal.agents.tools import ToolRegistry
from ai_terminal.utils.logger import get_logger


class TestSettings:
    """Test configuration management."""
    
    def test_settings_creation(self):
        """Test that settings can be created."""
        settings = Settings()
        assert settings.app_name == "ai-terminal"
        assert settings.version == "0.1.0"
        assert settings.default_provider == "openai"
    
    def test_settings_directories(self):
        """Test that settings create proper directories."""
        settings = Settings()
        assert isinstance(settings.config_dir, Path)
        assert isinstance(settings.data_dir, Path)
        assert isinstance(settings.cache_dir, Path)
    
    def test_provider_config(self):
        """Test provider configuration."""
        settings = Settings()
        openai_config = settings.get_provider_config("openai")
        assert "base_url" in openai_config
        assert "models" in openai_config


class TestMessage:
    """Test message handling."""
    
    def test_message_creation(self):
        """Test message creation."""
        message = Message(
            role=MessageRole.USER,
            content="Hello, world!"
        )
        assert message.role == MessageRole.USER
        assert message.content == "Hello, world!"
        assert message.timestamp is not None
    
    def test_message_roles(self):
        """Test message roles."""
        assert MessageRole.USER == "user"
        assert MessageRole.ASSISTANT == "assistant"
        assert MessageRole.SYSTEM == "system"


class TestToolRegistry:
    """Test tool registry functionality."""
    
    @pytest.fixture
    def tool_registry(self):
        """Create a tool registry for testing."""
        return ToolRegistry()
    
    def test_tool_registry_creation(self, tool_registry):
        """Test tool registry creation."""
        assert isinstance(tool_registry, ToolRegistry)
        assert len(tool_registry.tools) > 0  # Should have default tools
    
    def test_default_tools_registered(self, tool_registry):
        """Test that default tools are registered."""
        tool_names = list(tool_registry.tools.keys())
        expected_tools = [
            "execute_command",
            "read_file", 
            "write_file",
            "list_directory",
            "search_files",
            "get_system_info"
        ]
        
        for tool in expected_tools:
            assert tool in tool_names
    
    def test_tool_definitions_format(self, tool_registry):
        """Test tool definitions are in correct format."""
        definitions = tool_registry.get_tool_definitions()
        assert isinstance(definitions, list)
        
        for definition in definitions:
            assert "type" in definition
            assert definition["type"] == "function"
            assert "function" in definition
            
            function = definition["function"]
            assert "name" in function
            assert "description" in function
            assert "parameters" in function
    
    @pytest.mark.asyncio
    async def test_tool_execution_unknown(self, tool_registry):
        """Test execution of unknown tool."""
        result = await tool_registry.execute_tool("unknown_tool", {})
        assert not result.success
        assert "Unknown tool" in result.content


class TestFileOperations:
    """Test file operations."""
    
    @pytest.mark.asyncio
    async def test_read_nonexistent_file(self):
        """Test reading a non-existent file."""
        from ai_terminal.utils.file_ops import FileOperations
        
        file_ops = FileOperations()
        
        with pytest.raises(FileNotFoundError):
            await file_ops.read_file_safe(Path("nonexistent_file.txt"))
    
    @pytest.mark.asyncio
    async def test_write_and_read_file(self):
        """Test writing and reading a file."""
        from ai_terminal.utils.file_ops import FileOperations
        
        file_ops = FileOperations()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.txt"
            test_content = "Hello, AI Terminal!"
            
            # Write file
            success = await file_ops.write_file_safe(
                test_file,
                test_content,
                create_backup=False
            )
            assert success
            
            # Read file
            read_content = await file_ops.read_file_safe(test_file)
            assert read_content == test_content
    
    @pytest.mark.asyncio
    async def test_create_diff(self):
        """Test diff creation."""
        from ai_terminal.utils.file_ops import FileOperations
        
        file_ops = FileOperations()
        
        original = "line 1\nline 2\nline 3\n"
        modified = "line 1\nline 2 modified\nline 3\nline 4\n"
        
        diff = await file_ops.create_diff(original, modified, "test.txt")
        
        assert "@@" in diff  # Unified diff format
        assert "line 2 modified" in diff
        assert "line 4" in diff


class TestLogging:
    """Test logging functionality."""
    
    def test_logger_creation(self):
        """Test logger creation."""
        logger = get_logger("test")
        assert logger.name == "test"
    
    def test_logger_hierarchy(self):
        """Test logger hierarchy."""
        logger1 = get_logger("ai_terminal.test")
        logger2 = get_logger("ai_terminal.test.submodule")
        
        assert logger1.name == "ai_terminal.test"
        assert logger2.name == "ai_terminal.test.submodule"


class TestIntegration:
    """Integration tests."""
    
    def test_import_all_modules(self):
        """Test that all main modules can be imported."""
        # Test core imports
        from ai_terminal import __version__
        from ai_terminal.config.settings import Settings
        from ai_terminal.providers.base import BaseProvider
        from ai_terminal.agents.agent_loop import AgentLoop
        from ai_terminal.agents.tools import ToolRegistry
        from ai_terminal.utils.logger import get_logger
        
        assert __version__ == "0.1.0"
        assert Settings is not None
        assert BaseProvider is not None
        assert AgentLoop is not None
        assert ToolRegistry is not None
        assert get_logger is not None
    
    def test_settings_singleton(self):
        """Test that settings work as singleton."""
        settings1 = get_settings()
        settings2 = get_settings()
        
        # Should be the same instance
        assert settings1 is settings2
    
    @pytest.mark.asyncio
    async def test_tool_registry_system_info(self):
        """Test system info tool execution."""
        tool_registry = ToolRegistry()
        
        result = await tool_registry.execute_tool("get_system_info", {})
        
        # Should succeed and contain system information
        assert result.success
        assert "System:" in result.content
        assert "Memory:" in result.content


if __name__ == "__main__":
    pytest.main([__file__])
