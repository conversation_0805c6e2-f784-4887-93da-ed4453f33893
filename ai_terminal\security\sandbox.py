"""
Multi-platform sandboxing system.

Provides process isolation and security controls for command execution
with platform-specific implementations.
"""

import platform
import subprocess
import asyncio
from typing import List, Optional, Dict, Any
from pathlib import Path

from ai_terminal.utils.logger import get_logger
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class Sandbox:
    """Multi-platform sandbox for secure command execution."""
    
    def __init__(self):
        """Initialize sandbox."""
        self.system = platform.system()
        self.settings = get_settings()
        self.enabled = self.settings.security.sandbox_enabled
        
        # Platform-specific setup
        self._setup_platform()
    
    def _setup_platform(self):
        """Setup platform-specific sandboxing."""
        if self.system == "Linux":
            self._setup_linux()
        elif self.system == "Darwin":  # macOS
            self._setup_macos()
        elif self.system == "Windows":
            self._setup_windows()
        else:
            logger.warning(f"Sandboxing not supported on {self.system}")
            self.enabled = False
    
    def _setup_linux(self):
        """Setup Linux sandboxing using firejail."""
        try:
            # Check if firejail is available
            result = subprocess.run(
                ["which", "firejail"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("Firejail available for Linux sandboxing")
                self.sandbox_command = ["firejail", "--quiet"]
            else:
                logger.warning("Firejail not found, sandboxing disabled")
                self.enabled = False
                
        except Exception as e:
            logger.error(f"Failed to setup Linux sandboxing: {e}")
            self.enabled = False
    
    def _setup_macos(self):
        """Setup macOS sandboxing using native sandbox."""
        try:
            # Check if sandbox-exec is available
            result = subprocess.run(
                ["which", "sandbox-exec"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("sandbox-exec available for macOS sandboxing")
                self.sandbox_command = ["sandbox-exec", "-f", "/dev/stdin"]
            else:
                logger.warning("sandbox-exec not found, sandboxing disabled")
                self.enabled = False
                
        except Exception as e:
            logger.error(f"Failed to setup macOS sandboxing: {e}")
            self.enabled = False
    
    def _setup_windows(self):
        """Setup Windows sandboxing using restricted execution."""
        # Windows sandboxing is more limited
        logger.info("Windows sandboxing using restricted execution")
        self.sandbox_command = []  # No special command needed
    
    async def execute_command(
        self,
        command: List[str],
        working_directory: Optional[Path] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Execute a command in the sandbox.
        
        Args:
            command: Command to execute as list of strings
            working_directory: Working directory for execution
            timeout: Timeout in seconds
            env: Environment variables
            
        Returns:
            Dictionary with execution results
        """
        if not self.enabled:
            # Execute without sandboxing
            return await self._execute_unsandboxed(
                command, working_directory, timeout, env
            )
        
        if self.system == "Linux":
            return await self._execute_linux_sandboxed(
                command, working_directory, timeout, env
            )
        elif self.system == "Darwin":
            return await self._execute_macos_sandboxed(
                command, working_directory, timeout, env
            )
        elif self.system == "Windows":
            return await self._execute_windows_sandboxed(
                command, working_directory, timeout, env
            )
        else:
            return await self._execute_unsandboxed(
                command, working_directory, timeout, env
            )
    
    async def _execute_unsandboxed(
        self,
        command: List[str],
        working_directory: Optional[Path] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Execute command without sandboxing."""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=working_directory,
                env=env
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": f"Command timed out after {timeout} seconds",
                    "return_code": -1,
                    "sandboxed": False,
                }
            
            return {
                "success": process.returncode == 0,
                "stdout": stdout.decode('utf-8', errors='replace'),
                "stderr": stderr.decode('utf-8', errors='replace'),
                "return_code": process.returncode,
                "sandboxed": False,
            }
            
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": f"Execution failed: {e}",
                "return_code": -1,
                "sandboxed": False,
            }
    
    async def _execute_linux_sandboxed(
        self,
        command: List[str],
        working_directory: Optional[Path] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Execute command with Linux firejail sandboxing."""
        try:
            # Build firejail command
            sandbox_cmd = self.sandbox_command.copy()
            
            # Add security options
            sandbox_cmd.extend([
                "--noprofile",  # Don't use default profile
                "--private-tmp",  # Private /tmp
                "--nonetwork",  # No network access
                "--nosound",  # No sound
                "--novideo",  # No video
                "--nodvd",  # No DVD
                "--notv",  # No TV
                "--nou2f",  # No U2F
            ])
            
            # Add working directory if specified
            if working_directory:
                sandbox_cmd.extend(["--private-cwd", str(working_directory)])
            
            # Add the actual command
            sandbox_cmd.extend(command)
            
            return await self._execute_unsandboxed(
                sandbox_cmd, working_directory, timeout, env
            )
            
        except Exception as e:
            logger.error(f"Linux sandboxed execution failed: {e}")
            return await self._execute_unsandboxed(
                command, working_directory, timeout, env
            )
    
    async def _execute_macos_sandboxed(
        self,
        command: List[str],
        working_directory: Optional[Path] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Execute command with macOS sandbox-exec."""
        try:
            # Create sandbox profile
            sandbox_profile = """
            (version 1)
            (deny default)
            (allow process-exec)
            (allow file-read*)
            (allow file-write* (subpath "/tmp"))
            """
            
            if working_directory:
                sandbox_profile += f'(allow file-write* (subpath "{working_directory}"))\n'
            
            # Build sandbox-exec command
            sandbox_cmd = self.sandbox_command.copy()
            sandbox_cmd.extend(command)
            
            # Execute with sandbox profile
            process = await asyncio.create_subprocess_exec(
                *sandbox_cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=working_directory,
                env=env
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input=sandbox_profile.encode()),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": f"Command timed out after {timeout} seconds",
                    "return_code": -1,
                    "sandboxed": True,
                }
            
            return {
                "success": process.returncode == 0,
                "stdout": stdout.decode('utf-8', errors='replace'),
                "stderr": stderr.decode('utf-8', errors='replace'),
                "return_code": process.returncode,
                "sandboxed": True,
            }
            
        except Exception as e:
            logger.error(f"macOS sandboxed execution failed: {e}")
            return await self._execute_unsandboxed(
                command, working_directory, timeout, env
            )
    
    async def _execute_windows_sandboxed(
        self,
        command: List[str],
        working_directory: Optional[Path] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Execute command with Windows restricted execution."""
        try:
            # For Windows, we use basic restrictions
            # In a production environment, you might use Windows Sandbox or containers
            
            # Create restricted environment
            restricted_env = env.copy() if env else {}
            
            # Remove potentially dangerous environment variables
            dangerous_vars = [
                "PATH",  # Limit PATH to essential directories only
                "PATHEXT",
                "COMSPEC",
            ]
            
            for var in dangerous_vars:
                if var in restricted_env:
                    if var == "PATH":
                        # Keep only essential paths
                        restricted_env[var] = r"C:\Windows\System32;C:\Windows"
            
            return await self._execute_unsandboxed(
                command, working_directory, timeout, restricted_env
            )
            
        except Exception as e:
            logger.error(f"Windows sandboxed execution failed: {e}")
            return await self._execute_unsandboxed(
                command, working_directory, timeout, env
            )
    
    def is_enabled(self) -> bool:
        """Check if sandboxing is enabled."""
        return self.enabled
    
    def get_platform_info(self) -> Dict[str, Any]:
        """Get platform-specific sandboxing information."""
        return {
            "system": self.system,
            "enabled": self.enabled,
            "sandbox_command": getattr(self, "sandbox_command", []),
            "capabilities": self._get_capabilities(),
        }
    
    def _get_capabilities(self) -> List[str]:
        """Get sandboxing capabilities for current platform."""
        if not self.enabled:
            return []
        
        if self.system == "Linux":
            return [
                "process_isolation",
                "filesystem_restrictions",
                "network_isolation",
                "resource_limits",
            ]
        elif self.system == "Darwin":
            return [
                "process_isolation",
                "filesystem_restrictions",
                "system_call_filtering",
            ]
        elif self.system == "Windows":
            return [
                "environment_restrictions",
                "basic_isolation",
            ]
        else:
            return []
