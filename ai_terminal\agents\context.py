"""
Conversation context management.

Defines the ConversationContext class and related data structures
for managing AI conversation state.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid

from ai_terminal.providers.base import BaseProvider, Message, MessageRole


@dataclass
class ConversationContext:
    """Context for a conversation session."""
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    messages: List[Message] = field(default_factory=list)
    provider: Optional[BaseProvider] = None
    model: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    system_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def add_message(self, message: Message) -> None:
        """Add a message to the conversation."""
        self.messages.append(message)
        self.updated_at = datetime.now()
    
    def get_messages_for_api(self) -> List[Message]:
        """Get messages formatted for API calls."""
        api_messages = []
        
        # Add system message if present
        if self.system_message:
            api_messages.append(Message(
                role=MessageRole.SYSTEM,
                content=self.system_message
            ))
        
        # Add conversation messages
        api_messages.extend(self.messages)
        
        return api_messages
