"""
Ollama provider implementation for local models.

Supports local LLMs through Ollama with streaming capabilities.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, AsyncIterator
import aiohttp

from ai_terminal.providers.base import (
    BaseProvider,
    Message,
    MessageRole,
    StreamChunk,
    CompletionResponse,
    ProviderCapabilities,
    ProviderError,
    AuthenticationError,
    RateLimitError,
    ModelNotFoundError,
    InvalidRequestError,
    ServiceUnavailableError,
)
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class OllamaProvider(BaseProvider):
    """Ollama provider for local models."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            api_key=api_key,
            base_url=base_url or "http://localhost:11434",
            model=model or "llama2",
            **kwargs
        )
        self._session: Optional[aiohttp.ClientSession] = None
    
    @property
    def name(self) -> str:
        """Provider name."""
        return "ollama"
    
    @property
    def capabilities(self) -> ProviderCapabilities:
        """Provider capabilities."""
        return ProviderCapabilities(
            supports_streaming=True,
            supports_functions=False,  # Most local models don't support functions yet
            supports_tools=False,
            supports_vision=False,  # Depends on model
            supports_system_messages=True,
            max_context_length=4096,  # Varies by model
            max_output_tokens=2048,
        )
    
    async def initialize(self) -> None:
        """Initialize the Ollama client."""
        try:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.get("timeout", 120))
            )
            
            # Test connection by listing models
            await self.list_models()
            logger.info(f"Initialized {self.name} provider successfully")
            
        except Exception as e:
            raise ProviderError(f"Failed to initialize Ollama provider: {e}", self.name)
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self._session:
            await self._session.close()
            self._session = None
    
    async def list_models(self) -> List[str]:
        """List available models."""
        if not self._session:
            raise ProviderError("Provider not initialized", self.name)
        
        try:
            async with self._session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return [model["name"] for model in data.get("models", [])]
                else:
                    raise ServiceUnavailableError(
                        f"Ollama service unavailable (status: {response.status})",
                        self.name
                    )
        except aiohttp.ClientError as e:
            raise ServiceUnavailableError(f"Failed to connect to Ollama: {e}", self.name)
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return []
    
    def _format_messages_for_ollama(self, messages: List[Message]) -> str:
        """Format messages for Ollama API.
        
        Ollama typically expects a single prompt string rather than structured messages.
        """
        formatted_parts = []
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                formatted_parts.append(f"System: {msg.content}")
            elif msg.role == MessageRole.USER:
                formatted_parts.append(f"Human: {msg.content}")
            elif msg.role == MessageRole.ASSISTANT:
                formatted_parts.append(f"Assistant: {msg.content}")
        
        # Add final prompt for assistant response
        formatted_parts.append("Assistant:")
        
        return "\n\n".join(formatted_parts)
    
    async def complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> CompletionResponse:
        """Generate a completion."""
        if not self._session:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.model or "llama2"
        prompt = self._format_messages_for_ollama(messages)
        
        # Prepare request parameters
        request_data = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temperature,
            }
        }
        
        if max_tokens:
            request_data["options"]["num_predict"] = max_tokens
        
        # Add any additional parameters
        if "options" in kwargs:
            request_data["options"].update(kwargs["options"])
        
        try:
            async with self._session.post(
                f"{self.base_url}/api/generate",
                json=request_data
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ProviderError(f"Ollama request failed: {error_text}", self.name)
                
                data = await response.json()
                content = data.get("response", "")
                
                # Create response message
                response_message = Message(
                    role=MessageRole.ASSISTANT,
                    content=content,
                )
                
                return CompletionResponse(
                    content=content,
                    messages=messages + [response_message],
                    usage={
                        "prompt_eval_count": data.get("prompt_eval_count", 0),
                        "eval_count": data.get("eval_count", 0),
                    },
                    finish_reason="stop" if data.get("done", False) else "length",
                    metadata={"model": model, "provider": self.name},
                )
                
        except aiohttp.ClientError as e:
            raise ServiceUnavailableError(f"Failed to connect to Ollama: {e}", self.name)
        except json.JSONDecodeError as e:
            raise ProviderError(f"Invalid response from Ollama: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Completion failed: {e}", self.name)
    
    async def stream_complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[StreamChunk]:
        """Generate a streaming completion."""
        if not self._session:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.model or "llama2"
        prompt = self._format_messages_for_ollama(messages)
        
        # Prepare request parameters
        request_data = {
            "model": model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": temperature,
            }
        }
        
        if max_tokens:
            request_data["options"]["num_predict"] = max_tokens
        
        # Add any additional parameters
        if "options" in kwargs:
            request_data["options"].update(kwargs["options"])
        
        try:
            async with self._session.post(
                f"{self.base_url}/api/generate",
                json=request_data
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ProviderError(f"Ollama request failed: {error_text}", self.name)
                
                accumulated_content = ""
                
                async for line in response.content:
                    if not line:
                        continue
                    
                    try:
                        data = json.loads(line.decode('utf-8'))
                        
                        if "response" in data:
                            content_chunk = data["response"]
                            accumulated_content += content_chunk
                            
                            yield StreamChunk(
                                content=content_chunk,
                                is_complete=False,
                                metadata={"model": model, "provider": self.name},
                            )
                        
                        if data.get("done", False):
                            yield StreamChunk(
                                content="",
                                is_complete=True,
                                metadata={
                                    "model": model,
                                    "provider": self.name,
                                    "finish_reason": "stop",
                                    "usage": {
                                        "prompt_eval_count": data.get("prompt_eval_count", 0),
                                        "eval_count": data.get("eval_count", 0),
                                    }
                                },
                            )
                            break
                    
                    except json.JSONDecodeError:
                        # Skip invalid JSON lines
                        continue
                
        except aiohttp.ClientError as e:
            raise ServiceUnavailableError(f"Failed to connect to Ollama: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Streaming completion failed: {e}", self.name)
    
    async def validate_connection(self) -> bool:
        """Validate that Ollama is running and accessible."""
        try:
            if not self._session:
                await self.initialize()
            
            async with self._session.get(f"{self.base_url}/api/tags") as response:
                return response.status == 200
        except Exception:
            return False
    
    async def pull_model(self, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Pull a model from Ollama registry.
        
        Args:
            model_name: Name of the model to pull
            
        Yields:
            Progress updates during model download
        """
        if not self._session:
            raise ProviderError("Provider not initialized", self.name)
        
        request_data = {"name": model_name, "stream": True}
        
        try:
            async with self._session.post(
                f"{self.base_url}/api/pull",
                json=request_data
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ProviderError(f"Failed to pull model: {error_text}", self.name)
                
                async for line in response.content:
                    if not line:
                        continue
                    
                    try:
                        data = json.loads(line.decode('utf-8'))
                        yield data
                    except json.JSONDecodeError:
                        continue
                        
        except aiohttp.ClientError as e:
            raise ServiceUnavailableError(f"Failed to connect to Ollama: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Model pull failed: {e}", self.name)
