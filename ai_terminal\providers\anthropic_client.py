"""
Anthropic Claude provider implementation.

Supports Claude 3 family models with function calling and streaming.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, AsyncIterator
import anthropic
from anthropic import AsyncAnthropic

from ai_terminal.providers.base import (
    BaseProvider,
    Message,
    MessageRole,
    StreamChunk,
    CompletionResponse,
    ProviderCapabilities,
    ProviderError,
    AuthenticationError,
    RateLimitError,
    ModelNotFoundError,
    InvalidRequestError,
    ServiceUnavailableError,
)
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class AnthropicProvider(BaseProvider):
    """Anthropic Claude provider implementation."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ):
        super().__init__(api_key=api_key, base_url=base_url, model=model, **kwargs)
        self._client: Optional[AsyncAnthropic] = None
    
    @property
    def name(self) -> str:
        """Provider name."""
        return "anthropic"
    
    @property
    def capabilities(self) -> ProviderCapabilities:
        """Provider capabilities."""
        return ProviderCapabilities(
            supports_streaming=True,
            supports_functions=True,
            supports_tools=True,
            supports_vision=True,
            supports_system_messages=True,
            max_context_length=200000,  # Claude 3
            max_output_tokens=4096,
        )
    
    async def initialize(self) -> None:
        """Initialize the Anthropic client."""
        try:
            self._client = AsyncAnthropic(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.config.get("timeout", 60),
                max_retries=self.config.get("max_retries", 3),
            )
            
            logger.info(f"Initialized {self.name} provider successfully")
            
        except anthropic.AuthenticationError as e:
            raise AuthenticationError(f"Authentication failed: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Failed to initialize provider: {e}", self.name)
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self._client:
            await self._client.close()
            self._client = None
    
    async def list_models(self) -> List[str]:
        """List available models."""
        # Anthropic doesn't have a models endpoint, return known models
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0",
            "claude-instant-1.2",
        ]
    
    def _format_messages_for_anthropic(self, messages: List[Message]) -> tuple[str, List[Dict[str, Any]]]:
        """Format messages for Anthropic API.
        
        Anthropic uses a different format where system messages are separate
        and the conversation alternates between user and assistant.
        """
        system_message = ""
        formatted_messages = []
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                system_message = msg.content
            elif msg.role in [MessageRole.USER, MessageRole.ASSISTANT]:
                formatted_msg = {
                    "role": msg.role.value,
                    "content": msg.content,
                }
                formatted_messages.append(formatted_msg)
        
        return system_message, formatted_messages
    
    async def complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> CompletionResponse:
        """Generate a completion."""
        if not self._client:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.model or "claude-3-sonnet-20240229"
        max_tokens = max_tokens or 4096
        
        system_message, formatted_messages = self._format_messages_for_anthropic(messages)
        
        # Prepare request parameters
        request_params = {
            "model": model,
            "messages": formatted_messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False,
        }
        
        if system_message:
            request_params["system"] = system_message
        
        if tools:
            request_params["tools"] = tools
        
        # Add any additional parameters
        request_params.update(kwargs)
        
        try:
            response = await self._client.messages.create(**request_params)
            
            # Parse response
            content = ""
            tool_calls = []
            
            for content_block in response.content:
                if content_block.type == "text":
                    content += content_block.text
                elif content_block.type == "tool_use":
                    tool_calls.append({
                        "id": content_block.id,
                        "type": "function",
                        "function": {
                            "name": content_block.name,
                            "arguments": json.dumps(content_block.input),
                        }
                    })
            
            # Create response message
            response_message = Message(
                role=MessageRole.ASSISTANT,
                content=content,
                tool_calls=tool_calls if tool_calls else None,
            )
            
            return CompletionResponse(
                content=content,
                messages=messages + [response_message],
                usage={
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens,
                } if response.usage else None,
                tool_calls=tool_calls if tool_calls else None,
                finish_reason=response.stop_reason,
                metadata={"model": model, "provider": self.name},
            )
            
        except anthropic.AuthenticationError as e:
            raise AuthenticationError(f"Authentication failed: {e}", self.name)
        except anthropic.RateLimitError as e:
            raise RateLimitError(f"Rate limit exceeded: {e}", self.name)
        except anthropic.NotFoundError as e:
            raise ModelNotFoundError(f"Model not found: {e}", self.name)
        except anthropic.BadRequestError as e:
            raise InvalidRequestError(f"Invalid request: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Completion failed: {e}", self.name)
    
    async def stream_complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[StreamChunk]:
        """Generate a streaming completion."""
        if not self._client:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.model or "claude-3-sonnet-20240229"
        max_tokens = max_tokens or 4096
        
        system_message, formatted_messages = self._format_messages_for_anthropic(messages)
        
        # Prepare request parameters
        request_params = {
            "model": model,
            "messages": formatted_messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True,
        }
        
        if system_message:
            request_params["system"] = system_message
        
        if tools:
            request_params["tools"] = tools
        
        # Add any additional parameters
        request_params.update(kwargs)
        
        try:
            stream = await self._client.messages.create(**request_params)
            
            accumulated_content = ""
            accumulated_tool_calls = []
            current_tool_call = None
            
            async for event in stream:
                if event.type == "message_start":
                    continue
                elif event.type == "content_block_start":
                    if event.content_block.type == "tool_use":
                        current_tool_call = {
                            "id": event.content_block.id,
                            "type": "function",
                            "function": {
                                "name": event.content_block.name,
                                "arguments": "",
                            }
                        }
                elif event.type == "content_block_delta":
                    if event.delta.type == "text_delta":
                        content_chunk = event.delta.text
                        accumulated_content += content_chunk
                        yield StreamChunk(
                            content=content_chunk,
                            is_complete=False,
                            metadata={"model": model, "provider": self.name},
                        )
                    elif event.delta.type == "input_json_delta" and current_tool_call:
                        current_tool_call["function"]["arguments"] += event.delta.partial_json
                elif event.type == "content_block_stop":
                    if current_tool_call:
                        accumulated_tool_calls.append(current_tool_call)
                        current_tool_call = None
                elif event.type == "message_delta":
                    if event.delta.stop_reason:
                        yield StreamChunk(
                            content="",
                            is_complete=True,
                            tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                            metadata={
                                "model": model,
                                "provider": self.name,
                                "finish_reason": event.delta.stop_reason,
                            },
                        )
                        break
                elif event.type == "message_stop":
                    yield StreamChunk(
                        content="",
                        is_complete=True,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                        metadata={
                            "model": model,
                            "provider": self.name,
                            "finish_reason": "stop",
                        },
                    )
                    break
            
        except anthropic.AuthenticationError as e:
            raise AuthenticationError(f"Authentication failed: {e}", self.name)
        except anthropic.RateLimitError as e:
            raise RateLimitError(f"Rate limit exceeded: {e}", self.name)
        except anthropic.NotFoundError as e:
            raise ModelNotFoundError(f"Model not found: {e}", self.name)
        except anthropic.BadRequestError as e:
            raise InvalidRequestError(f"Invalid request: {e}", self.name)
        except Exception as e:
            raise ProviderError(f"Streaming completion failed: {e}", self.name)
