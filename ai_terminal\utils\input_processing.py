"""
Multi-modal input processing utilities.

Handles text and image input processing, format detection,
and context-aware file inclusion for AI interactions.
"""

import asyncio
import base64
import mimetypes
from typing import List, Dict, Optional, Any, Tuple, Union
from pathlib import Path
import json

import aiofiles

from ai_terminal.utils.logger import get_logger
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class InputProcessor:
    """Multi-modal input processor."""
    
    def __init__(self):
        """Initialize input processor."""
        self.settings = get_settings()
        
        # Supported image formats
        self.image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        
        # Supported text formats
        self.text_formats = {
            '.txt', '.md', '.py', '.js', '.ts', '.html', '.css', '.json',
            '.yaml', '.yml', '.xml', '.csv', '.log', '.sh', '.bat', '.ps1'
        }
        
        # Binary formats to avoid
        self.binary_formats = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.zip', '.tar', '.gz',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
        }
    
    async def process_input(
        self,
        content: str,
        files: Optional[List[str]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process multi-modal input.
        
        Args:
            content: Text content
            files: List of file paths to include
            context: Additional context information
            
        Returns:
            Processed input data
        """
        try:
            result = {
                "text": content,
                "files": [],
                "images": [],
                "metadata": context or {},
                "total_size": len(content.encode('utf-8')),
            }
            
            if files:
                for file_path in files:
                    file_data = await self._process_file(file_path)
                    if file_data:
                        if file_data["type"] == "image":
                            result["images"].append(file_data)
                        else:
                            result["files"].append(file_data)
                        
                        result["total_size"] += file_data.get("size", 0)
            
            # Check total size limits
            max_size = self.settings.security.max_file_size * 5  # Allow larger total
            if result["total_size"] > max_size:
                logger.warning(f"Input size {result['total_size']} exceeds limit {max_size}")
                result["warning"] = f"Input size exceeds recommended limit"
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process input: {e}")
            return {
                "text": content,
                "files": [],
                "images": [],
                "metadata": {"error": str(e)},
                "total_size": len(content.encode('utf-8')),
            }
    
    async def _process_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process a single file.
        
        Args:
            file_path: Path to file
            
        Returns:
            File data dictionary or None if failed
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                logger.warning(f"File not found: {file_path}")
                return None
            
            if not path.is_file():
                logger.warning(f"Not a file: {file_path}")
                return None
            
            # Get file info
            stat = path.stat()
            file_size = stat.st_size
            
            # Check size limit
            if file_size > self.settings.security.max_file_size:
                logger.warning(f"File too large: {file_path} ({file_size} bytes)")
                return {
                    "path": str(path),
                    "name": path.name,
                    "type": "error",
                    "error": f"File too large ({file_size} bytes)",
                    "size": file_size,
                }
            
            # Determine file type
            file_type = self._detect_file_type(path)
            
            if file_type == "image":
                return await self._process_image_file(path)
            elif file_type == "text":
                return await self._process_text_file(path)
            elif file_type == "binary":
                return {
                    "path": str(path),
                    "name": path.name,
                    "type": "binary",
                    "error": "Binary file not supported",
                    "size": file_size,
                }
            else:
                # Try to process as text
                return await self._process_text_file(path)
                
        except Exception as e:
            logger.error(f"Failed to process file {file_path}: {e}")
            return {
                "path": file_path,
                "type": "error",
                "error": str(e),
                "size": 0,
            }
    
    def _detect_file_type(self, path: Path) -> str:
        """Detect file type based on extension and content.
        
        Args:
            path: File path
            
        Returns:
            File type: 'image', 'text', 'binary', or 'unknown'
        """
        suffix = path.suffix.lower()
        
        if suffix in self.image_formats:
            return "image"
        elif suffix in self.text_formats:
            return "text"
        elif suffix in self.binary_formats:
            return "binary"
        else:
            # Try to detect by MIME type
            mime_type, _ = mimetypes.guess_type(str(path))
            if mime_type:
                if mime_type.startswith('image/'):
                    return "image"
                elif mime_type.startswith('text/'):
                    return "text"
                elif mime_type.startswith('application/'):
                    return "binary"
            
            return "unknown"
    
    async def _process_text_file(self, path: Path) -> Dict[str, Any]:
        """Process a text file.
        
        Args:
            path: File path
            
        Returns:
            File data dictionary
        """
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            content = None
            encoding_used = None
            
            for encoding in encodings:
                try:
                    async with aiofiles.open(path, 'r', encoding=encoding) as f:
                        content = await f.read()
                    encoding_used = encoding
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                return {
                    "path": str(path),
                    "name": path.name,
                    "type": "error",
                    "error": "Could not decode file",
                    "size": path.stat().st_size,
                }
            
            # Truncate if too long
            max_length = 10000  # Reasonable limit for context
            truncated = False
            if len(content) > max_length:
                content = content[:max_length]
                truncated = True
            
            return {
                "path": str(path),
                "name": path.name,
                "type": "text",
                "content": content,
                "encoding": encoding_used,
                "size": path.stat().st_size,
                "truncated": truncated,
                "lines": len(content.splitlines()),
            }
            
        except Exception as e:
            return {
                "path": str(path),
                "name": path.name,
                "type": "error",
                "error": str(e),
                "size": path.stat().st_size,
            }
    
    async def _process_image_file(self, path: Path) -> Dict[str, Any]:
        """Process an image file.
        
        Args:
            path: File path
            
        Returns:
            File data dictionary
        """
        try:
            # Read image as base64
            async with aiofiles.open(path, 'rb') as f:
                image_data = await f.read()
            
            # Encode as base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(str(path))
            if not mime_type:
                mime_type = 'image/jpeg'  # Default
            
            return {
                "path": str(path),
                "name": path.name,
                "type": "image",
                "data": base64_data,
                "mime_type": mime_type,
                "size": len(image_data),
                "data_url": f"data:{mime_type};base64,{base64_data}",
            }
            
        except Exception as e:
            return {
                "path": str(path),
                "name": path.name,
                "type": "error",
                "error": str(e),
                "size": path.stat().st_size,
            }
    
    def format_for_ai(self, processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """Format processed input for AI consumption.
        
        Args:
            processed_input: Processed input data
            
        Returns:
            Formatted data for AI
        """
        try:
            # Start with text content
            content_parts = [processed_input["text"]]
            
            # Add text files
            for file_data in processed_input["files"]:
                if file_data["type"] == "text":
                    content_parts.append(
                        f"\n--- File: {file_data['name']} ---\n"
                        f"{file_data['content']}\n"
                        f"--- End of {file_data['name']} ---\n"
                    )
                elif file_data["type"] == "error":
                    content_parts.append(f"\n[Error with file {file_data['name']}: {file_data['error']}]\n")
            
            # Combine text content
            combined_text = "\n".join(content_parts)
            
            # Prepare result
            result = {
                "text": combined_text,
                "metadata": processed_input["metadata"],
            }
            
            # Add images if present (for vision-capable models)
            if processed_input["images"]:
                result["images"] = []
                for image_data in processed_input["images"]:
                    if image_data["type"] == "image":
                        result["images"].append({
                            "type": "image_url",
                            "image_url": {
                                "url": image_data["data_url"]
                            }
                        })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to format input for AI: {e}")
            return {
                "text": processed_input["text"],
                "metadata": {"error": str(e)},
            }
    
    def get_context_summary(self, processed_input: Dict[str, Any]) -> str:
        """Get a summary of the input context.
        
        Args:
            processed_input: Processed input data
            
        Returns:
            Context summary string
        """
        try:
            parts = []
            
            # Text length
            text_length = len(processed_input["text"])
            if text_length > 0:
                parts.append(f"{text_length} characters of text")
            
            # Files
            file_count = len(processed_input["files"])
            if file_count > 0:
                text_files = sum(1 for f in processed_input["files"] if f["type"] == "text")
                if text_files > 0:
                    parts.append(f"{text_files} text file(s)")
                
                error_files = sum(1 for f in processed_input["files"] if f["type"] == "error")
                if error_files > 0:
                    parts.append(f"{error_files} file error(s)")
            
            # Images
            image_count = len(processed_input["images"])
            if image_count > 0:
                parts.append(f"{image_count} image(s)")
            
            # Total size
            total_size = processed_input["total_size"]
            if total_size > 1024 * 1024:
                size_str = f"{total_size / (1024 * 1024):.1f} MB"
            elif total_size > 1024:
                size_str = f"{total_size / 1024:.1f} KB"
            else:
                size_str = f"{total_size} bytes"
            
            parts.append(f"total size: {size_str}")
            
            if not parts:
                return "No content"
            
            return ", ".join(parts)
            
        except Exception as e:
            logger.error(f"Failed to get context summary: {e}")
            return "Unknown content"
