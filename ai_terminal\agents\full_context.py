"""
Full context processing for directory-wide analysis.

Provides intelligent context gathering, file filtering, and batch operations
with atomic transactions and context optimization.
"""

import asyncio
import os
from typing import List, Dict, Optional, Any, Set, Tuple
from pathlib import Path
import fnmatch
import hashlib
from dataclasses import dataclass, field
from datetime import datetime

import aiofiles

from ai_terminal.utils.logger import get_logger
from ai_terminal.utils.file_ops import FileOperations
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


@dataclass
class FileContext:
    """Context information for a file."""
    path: Path
    relative_path: str
    size: int
    modified_time: datetime
    file_type: str
    content: Optional[str] = None
    content_hash: Optional[str] = None
    language: Optional[str] = None
    importance_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DirectoryContext:
    """Context information for a directory."""
    root_path: Path
    files: List[FileContext] = field(default_factory=list)
    total_size: int = 0
    file_count: int = 0
    languages: Set[str] = field(default_factory=set)
    structure: Dict[str, Any] = field(default_factory=dict)
    summary: str = ""
    created_at: datetime = field(default_factory=datetime.now)


class FullContextProcessor:
    """Processor for full directory context analysis."""
    
    def __init__(self):
        """Initialize context processor."""
        self.settings = get_settings()
        self.file_ops = FileOperations()
        
        # File type mappings
        self.language_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'zsh',
            '.fish': 'fish',
            '.ps1': 'powershell',
            '.bat': 'batch',
            '.cmd': 'batch',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.xml': 'xml',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.toml': 'toml',
            '.ini': 'ini',
            '.cfg': 'ini',
            '.conf': 'config',
            '.md': 'markdown',
            '.rst': 'rst',
            '.txt': 'text',
            '.log': 'log',
            '.sql': 'sql',
            '.r': 'r',
            '.R': 'r',
            '.m': 'matlab',
            '.pl': 'perl',
            '.lua': 'lua',
            '.vim': 'vim',
            '.dockerfile': 'dockerfile',
            '.makefile': 'makefile',
        }
        
        # Default ignore patterns
        self.default_ignore_patterns = [
            '*.pyc', '*.pyo', '*.pyd', '__pycache__',
            '*.so', '*.dll', '*.dylib',
            '.git', '.svn', '.hg', '.bzr',
            'node_modules', '.npm', '.yarn',
            '.venv', 'venv', '.env',
            '*.egg-info', 'dist', 'build',
            '.pytest_cache', '.coverage',
            '*.log', '*.tmp', '*.temp',
            '.DS_Store', 'Thumbs.db',
            '*.min.js', '*.min.css',
            '*.map', '*.lock',
        ]
        
        # Important file patterns (higher priority)
        self.important_patterns = [
            'README*', 'readme*',
            'LICENSE*', 'license*',
            'CHANGELOG*', 'changelog*',
            'pyproject.toml', 'setup.py', 'requirements.txt',
            'package.json', 'package-lock.json',
            'Cargo.toml', 'Cargo.lock',
            'go.mod', 'go.sum',
            'Makefile', 'makefile',
            'Dockerfile', 'docker-compose.yml',
            '.gitignore', '.gitattributes',
            'main.py', 'app.py', 'index.js', 'main.js',
        ]
    
    async def analyze_directory(
        self,
        directory_path: Path,
        max_files: int = 100,
        max_total_size: int = 10_000_000,  # 10MB
        include_patterns: Optional[List[str]] = None,
        exclude_patterns: Optional[List[str]] = None,
        recursive: bool = True,
        max_depth: int = 10
    ) -> DirectoryContext:
        """Analyze a directory and gather full context.
        
        Args:
            directory_path: Path to directory to analyze
            max_files: Maximum number of files to include
            max_total_size: Maximum total size of content
            include_patterns: Patterns for files to include
            exclude_patterns: Additional patterns to exclude
            recursive: Whether to search recursively
            max_depth: Maximum recursion depth
            
        Returns:
            DirectoryContext with analysis results
        """
        try:
            logger.info(f"Analyzing directory: {directory_path}")
            
            # Initialize context
            context = DirectoryContext(root_path=directory_path)
            
            # Gather file list
            files = await self._gather_files(
                directory_path,
                include_patterns or [],
                exclude_patterns or [],
                recursive,
                max_depth
            )
            
            # Score and filter files
            scored_files = await self._score_files(files)
            
            # Select best files within limits
            selected_files = self._select_files(
                scored_files,
                max_files,
                max_total_size
            )
            
            # Load file contents
            context.files = await self._load_file_contents(selected_files)
            
            # Calculate statistics
            context.total_size = sum(f.size for f in context.files)
            context.file_count = len(context.files)
            context.languages = {f.language for f in context.files if f.language}
            
            # Generate structure
            context.structure = self._generate_structure(context.files)
            
            # Generate summary
            context.summary = self._generate_summary(context)
            
            logger.info(
                f"Directory analysis complete: {context.file_count} files, "
                f"{context.total_size} bytes, {len(context.languages)} languages"
            )
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to analyze directory {directory_path}: {e}")
            raise
    
    async def _gather_files(
        self,
        directory_path: Path,
        include_patterns: List[str],
        exclude_patterns: List[str],
        recursive: bool,
        max_depth: int
    ) -> List[Path]:
        """Gather list of files to analyze."""
        files = []
        all_exclude_patterns = self.default_ignore_patterns + exclude_patterns
        
        def should_include(path: Path) -> bool:
            """Check if file should be included."""
            # Check exclude patterns
            for pattern in all_exclude_patterns:
                if fnmatch.fnmatch(path.name, pattern) or fnmatch.fnmatch(str(path), pattern):
                    return False
            
            # Check include patterns (if specified)
            if include_patterns:
                for pattern in include_patterns:
                    if fnmatch.fnmatch(path.name, pattern) or fnmatch.fnmatch(str(path), pattern):
                        return True
                return False
            
            # Default: include text files
            return self._is_text_file(path)
        
        def walk_directory(current_path: Path, depth: int = 0):
            """Walk directory recursively."""
            if depth > max_depth:
                return
            
            try:
                for item in current_path.iterdir():
                    if item.is_file():
                        if should_include(item):
                            files.append(item)
                    elif item.is_dir() and recursive:
                        # Skip hidden directories
                        if not item.name.startswith('.'):
                            walk_directory(item, depth + 1)
            except PermissionError:
                logger.warning(f"Permission denied: {current_path}")
        
        walk_directory(directory_path)
        return files
    
    def _is_text_file(self, path: Path) -> bool:
        """Check if file is likely a text file."""
        # Check by extension
        if path.suffix.lower() in self.language_extensions:
            return True
        
        # Check by name patterns
        for pattern in self.important_patterns:
            if fnmatch.fnmatch(path.name, pattern):
                return True
        
        # Check file size (skip very large files)
        try:
            if path.stat().st_size > 1_000_000:  # 1MB
                return False
        except OSError:
            return False
        
        return True
    
    async def _score_files(self, files: List[Path]) -> List[Tuple[Path, float]]:
        """Score files by importance."""
        scored_files = []
        
        for file_path in files:
            try:
                score = 0.0
                
                # Base score
                score += 1.0
                
                # Important file patterns
                for pattern in self.important_patterns:
                    if fnmatch.fnmatch(file_path.name, pattern):
                        score += 5.0
                        break
                
                # Language bonus
                if file_path.suffix.lower() in self.language_extensions:
                    score += 2.0
                
                # Size penalty (prefer smaller files)
                try:
                    size = file_path.stat().st_size
                    if size > 100_000:  # 100KB
                        score -= 1.0
                    elif size > 10_000:  # 10KB
                        score -= 0.5
                except OSError:
                    score -= 2.0  # Penalty for inaccessible files
                
                # Depth penalty (prefer files closer to root)
                depth = len(file_path.relative_to(files[0].parent if files else Path()).parts)
                score -= depth * 0.1
                
                scored_files.append((file_path, score))
                
            except Exception as e:
                logger.warning(f"Failed to score file {file_path}: {e}")
                scored_files.append((file_path, 0.0))
        
        # Sort by score (highest first)
        scored_files.sort(key=lambda x: x[1], reverse=True)
        return scored_files
    
    def _select_files(
        self,
        scored_files: List[Tuple[Path, float]],
        max_files: int,
        max_total_size: int
    ) -> List[Path]:
        """Select files within limits."""
        selected = []
        total_size = 0
        
        for file_path, score in scored_files:
            if len(selected) >= max_files:
                break
            
            try:
                file_size = file_path.stat().st_size
                if total_size + file_size > max_total_size:
                    # Skip if it would exceed size limit
                    continue
                
                selected.append(file_path)
                total_size += file_size
                
            except OSError:
                # Skip inaccessible files
                continue
        
        return selected
    
    async def _load_file_contents(self, files: List[Path]) -> List[FileContext]:
        """Load contents of selected files."""
        file_contexts = []
        
        for file_path in files:
            try:
                stat = file_path.stat()
                
                # Create basic context
                context = FileContext(
                    path=file_path,
                    relative_path=str(file_path.relative_to(file_path.parents[len(file_path.parents)-1])),
                    size=stat.st_size,
                    modified_time=datetime.fromtimestamp(stat.st_mtime),
                    file_type=self._get_file_type(file_path),
                    language=self.language_extensions.get(file_path.suffix.lower()),
                )
                
                # Load content if reasonable size
                if stat.st_size <= 100_000:  # 100KB limit
                    try:
                        content = await self.file_ops.read_file_safe(file_path)
                        context.content = content
                        context.content_hash = hashlib.md5(content.encode()).hexdigest()
                    except Exception as e:
                        logger.warning(f"Failed to read {file_path}: {e}")
                        context.metadata["read_error"] = str(e)
                
                file_contexts.append(context)
                
            except Exception as e:
                logger.warning(f"Failed to process {file_path}: {e}")
        
        return file_contexts
    
    def _get_file_type(self, path: Path) -> str:
        """Get file type description."""
        suffix = path.suffix.lower()
        
        if suffix in self.language_extensions:
            return f"{self.language_extensions[suffix]} source"
        elif suffix in ['.txt', '.md', '.rst']:
            return "documentation"
        elif suffix in ['.json', '.yaml', '.yml', '.toml', '.ini']:
            return "configuration"
        elif suffix in ['.log']:
            return "log file"
        else:
            return "text file"
    
    def _generate_structure(self, files: List[FileContext]) -> Dict[str, Any]:
        """Generate directory structure representation."""
        structure = {}
        
        for file_context in files:
            parts = Path(file_context.relative_path).parts
            current = structure
            
            for part in parts[:-1]:  # Directories
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # File
            filename = parts[-1] if parts else file_context.path.name
            current[filename] = {
                "type": "file",
                "size": file_context.size,
                "language": file_context.language,
                "file_type": file_context.file_type,
            }
        
        return structure
    
    def _generate_summary(self, context: DirectoryContext) -> str:
        """Generate a summary of the directory context."""
        parts = []
        
        # Basic stats
        parts.append(f"Directory: {context.root_path}")
        parts.append(f"Files analyzed: {context.file_count}")
        parts.append(f"Total size: {context.total_size:,} bytes")
        
        # Languages
        if context.languages:
            lang_list = ", ".join(sorted(context.languages))
            parts.append(f"Languages: {lang_list}")
        
        # Important files
        important_files = [
            f.relative_path for f in context.files
            if any(fnmatch.fnmatch(f.path.name, pattern) for pattern in self.important_patterns)
        ]
        
        if important_files:
            parts.append(f"Key files: {', '.join(important_files[:5])}")
        
        return "\n".join(parts)
    
    def format_for_ai(self, context: DirectoryContext, include_content: bool = True) -> str:
        """Format directory context for AI consumption.
        
        Args:
            context: Directory context
            include_content: Whether to include file contents
            
        Returns:
            Formatted string for AI
        """
        parts = []
        
        # Header
        parts.append("=== DIRECTORY CONTEXT ===")
        parts.append(context.summary)
        parts.append("")
        
        # Structure overview
        parts.append("=== DIRECTORY STRUCTURE ===")
        parts.append(self._format_structure(context.structure))
        parts.append("")
        
        # File contents
        if include_content:
            parts.append("=== FILE CONTENTS ===")
            
            for file_context in context.files:
                if file_context.content:
                    parts.append(f"\n--- {file_context.relative_path} ---")
                    parts.append(f"Type: {file_context.file_type}")
                    if file_context.language:
                        parts.append(f"Language: {file_context.language}")
                    parts.append(f"Size: {file_context.size:,} bytes")
                    parts.append("")
                    parts.append(file_context.content)
                    parts.append(f"\n--- End of {file_context.relative_path} ---")
        
        return "\n".join(parts)
    
    def _format_structure(self, structure: Dict[str, Any], indent: int = 0) -> str:
        """Format directory structure as tree."""
        lines = []
        prefix = "  " * indent
        
        for name, content in structure.items():
            if isinstance(content, dict):
                if content.get("type") == "file":
                    # File
                    size = content.get("size", 0)
                    lang = content.get("language", "")
                    lang_suffix = f" ({lang})" if lang else ""
                    lines.append(f"{prefix}📄 {name} ({size:,} bytes){lang_suffix}")
                else:
                    # Directory
                    lines.append(f"{prefix}📁 {name}/")
                    lines.append(self._format_structure(content, indent + 1))
        
        return "\n".join(lines)
