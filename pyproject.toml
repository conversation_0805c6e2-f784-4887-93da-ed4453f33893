[tool.poetry]
name = "ai-terminal"
version = "0.1.0"
description = "Sophisticated AI-powered CLI terminal tool with advanced features"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "ai_terminal"}]

[tool.poetry.dependencies]
python = "^3.9"
# Core dependencies
typer = {extras = ["all"], version = "^0.9.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
rich = "^13.7.0"
textual = "^0.45.0"

# AI providers
openai = "^1.6.0"
anthropic = "^0.8.0"
aiohttp = "^3.9.0"

# File and I/O operations
aiofiles = "^23.2.0"
PyYAML = "^6.0.1"
watchdog = "^3.0.0"

# System utilities
psutil = "^5.9.0"
click = "^8.1.0"

# Notifications (cross-platform)
plyer = "^2.1.0"
win10toast = {version = "^0.9", markers = "sys_platform == 'win32'"}

# Development dependencies
[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.12.0"
ruff = "^0.1.8"
mypy = "^1.8.0"
pre-commit = "^3.6.0"

[tool.poetry.scripts]
ai-terminal = "cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py39"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.9"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"
