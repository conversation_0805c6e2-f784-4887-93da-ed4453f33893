"""
Advanced input system with slash commands and file tag expansion.

Provides enhanced input capabilities including command completion,
file tag expansion, and intelligent suggestions.
"""

import asyncio
import re
from typing import List, Dict, Optional, Tuple, Callable
from pathlib import Path
import glob

from rich.console import Console
from rich.text import Text
from rich.prompt import Prompt

from ai_terminal.utils.logger import get_logger
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class AdvancedInput:
    """Advanced input system with enhanced capabilities."""
    
    def __init__(self):
        """Initialize advanced input system."""
        self.console = Console()
        self.settings = get_settings()
        
        # Command history
        self.history: List[str] = []
        self.history_index = -1
        
        # Slash commands
        self.slash_commands = {
            "/help": "Show available commands",
            "/clear": "Clear conversation history",
            "/compact": "Toggle compact display mode",
            "/history": "Show conversation history",
            "/sessions": "Manage saved sessions",
            "/model": "Switch AI model or provider",
            "/diff": "Show file changes and patches",
            "/exit": "Exit the application",
            "/quit": "Exit the application",
        }
        
        # File completion cache
        self._file_cache: Dict[str, List[str]] = {}
        self._cache_timestamp: Dict[str, float] = {}
        
        # Callbacks
        self.on_command: Optional[Callable[[str], None]] = None
        self.on_file_tag: Optional[Callable[[str], str]] = None
    
    async def get_input(self, prompt_text: str = "You> ") -> str:
        """Get enhanced input with completion and expansion.
        
        Args:
            prompt_text: Prompt text to display
            
        Returns:
            User input with expansions applied
        """
        try:
            # Get raw input
            raw_input = self._get_raw_input(prompt_text)
            
            # Add to history
            if raw_input.strip():
                self.history.append(raw_input)
                self.history_index = len(self.history)
            
            # Process file tags
            processed_input = await self._process_file_tags(raw_input)
            
            # Process slash commands
            if processed_input.startswith('/'):
                return processed_input
            
            return processed_input
            
        except KeyboardInterrupt:
            return "/exit"
        except EOFError:
            return "/exit"
    
    def _get_raw_input(self, prompt_text: str) -> str:
        """Get raw input from user."""
        # For now, use simple input
        # In a full implementation, this would use a more sophisticated
        # input system with readline-like capabilities
        
        prompt = Text()
        prompt.append(prompt_text, style="bold blue")
        
        self.console.print(prompt, end="")
        return input()
    
    async def _process_file_tags(self, text: str) -> str:
        """Process @file.txt tags in input.
        
        Args:
            text: Input text with potential file tags
            
        Returns:
            Text with file tags expanded
        """
        # Find all @filename patterns
        file_pattern = r'@([^\s]+)'
        matches = re.finditer(file_pattern, text)
        
        processed_text = text
        offset = 0
        
        for match in matches:
            file_path = match.group(1)
            start, end = match.span()
            
            # Adjust positions for previous replacements
            start += offset
            end += offset
            
            # Expand file tag
            expanded = await self._expand_file_tag(file_path)
            
            # Replace in text
            processed_text = processed_text[:start] + expanded + processed_text[end:]
            offset += len(expanded) - (end - start)
        
        return processed_text
    
    async def _expand_file_tag(self, file_path: str) -> str:
        """Expand a file tag to include file content.
        
        Args:
            file_path: Path to file
            
        Returns:
            Expanded text with file content
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                # Try to find similar files
                suggestions = self._find_similar_files(file_path)
                if suggestions:
                    suggestion_text = ", ".join(suggestions[:3])
                    return f"[File not found: {file_path}. Did you mean: {suggestion_text}?]"
                else:
                    return f"[File not found: {file_path}]"
            
            if not path.is_file():
                return f"[Not a file: {file_path}]"
            
            # Check file size
            if path.stat().st_size > self.settings.security.max_file_size:
                return f"[File too large: {file_path}]"
            
            # Read file content
            try:
                content = path.read_text(encoding='utf-8')
                
                # Truncate if too long
                max_length = 2000  # Reasonable limit for chat context
                if len(content) > max_length:
                    content = content[:max_length] + "\n... [truncated]"
                
                return f"\n--- File: {file_path} ---\n{content}\n--- End of {file_path} ---\n"
                
            except UnicodeDecodeError:
                return f"[Binary file: {file_path}]"
            except Exception as e:
                return f"[Error reading {file_path}: {e}]"
                
        except Exception as e:
            logger.error(f"Failed to expand file tag {file_path}: {e}")
            return f"[Error: {file_path}]"
    
    def _find_similar_files(self, pattern: str) -> List[str]:
        """Find files similar to the given pattern.
        
        Args:
            pattern: File pattern to search for
            
        Returns:
            List of similar file paths
        """
        try:
            # Try glob patterns
            matches = []
            
            # Direct glob
            matches.extend(glob.glob(pattern))
            
            # Case-insensitive search
            if not matches:
                for path in Path('.').rglob('*'):
                    if path.is_file() and pattern.lower() in path.name.lower():
                        matches.append(str(path))
                        if len(matches) >= 5:
                            break
            
            return matches[:5]
            
        except Exception:
            return []
    
    def get_file_completions(self, partial_path: str) -> List[str]:
        """Get file completions for partial path.
        
        Args:
            partial_path: Partial file path
            
        Returns:
            List of completion suggestions
        """
        try:
            # Use glob for completion
            if '*' not in partial_path and '?' not in partial_path:
                partial_path += '*'
            
            matches = glob.glob(partial_path)
            
            # Limit results
            return sorted(matches[:20])
            
        except Exception:
            return []
    
    def get_slash_command_completions(self, partial_command: str) -> List[str]:
        """Get slash command completions.
        
        Args:
            partial_command: Partial slash command
            
        Returns:
            List of matching commands
        """
        if not partial_command.startswith('/'):
            return []
        
        matches = []
        for cmd in self.slash_commands:
            if cmd.startswith(partial_command):
                matches.append(cmd)
        
        return sorted(matches)
    
    def show_slash_commands(self) -> None:
        """Show available slash commands."""
        self.console.print("\n[bold]Available Commands:[/bold]")
        
        for cmd, description in self.slash_commands.items():
            self.console.print(f"  [cyan]{cmd}[/cyan] - {description}")
        
        self.console.print()
    
    def add_to_history(self, command: str) -> None:
        """Add command to history.
        
        Args:
            command: Command to add
        """
        if command.strip() and (not self.history or self.history[-1] != command):
            self.history.append(command)
            
            # Limit history size
            max_history = 1000
            if len(self.history) > max_history:
                self.history = self.history[-max_history:]
            
            self.history_index = len(self.history)
    
    def get_history_item(self, direction: int) -> Optional[str]:
        """Get item from history.
        
        Args:
            direction: -1 for previous, 1 for next
            
        Returns:
            History item or None
        """
        if not self.history:
            return None
        
        self.history_index += direction
        
        if self.history_index < 0:
            self.history_index = 0
        elif self.history_index >= len(self.history):
            self.history_index = len(self.history)
            return ""
        
        return self.history[self.history_index]
    
    def search_history(self, query: str) -> List[str]:
        """Search command history.
        
        Args:
            query: Search query
            
        Returns:
            List of matching commands
        """
        if not query:
            return self.history[-20:]  # Return recent commands
        
        matches = []
        query_lower = query.lower()
        
        for cmd in reversed(self.history):
            if query_lower in cmd.lower():
                matches.append(cmd)
                if len(matches) >= 20:
                    break
        
        return matches
    
    def clear_history(self) -> None:
        """Clear command history."""
        self.history.clear()
        self.history_index = -1
    
    def set_callbacks(
        self,
        on_command: Optional[Callable[[str], None]] = None,
        on_file_tag: Optional[Callable[[str], str]] = None
    ) -> None:
        """Set callback functions.
        
        Args:
            on_command: Callback for command execution
            on_file_tag: Callback for file tag processing
        """
        self.on_command = on_command
        self.on_file_tag = on_file_tag
