"""
Command history management.

Handles secure storage and retrieval of command history with
filtering for sensitive data and search capabilities.
"""

import asyncio
import json
from typing import List, Dict, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
import re
import aiofiles

from ai_terminal.config.settings import get_settings
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class HistoryManager:
    """Manages command history with security filtering."""
    
    def __init__(self):
        """Initialize history manager."""
        self.settings = get_settings()
        self.history_dir = self.settings.data_dir / "history"
        self.history_dir.mkdir(parents=True, exist_ok=True)
        
        self.history_file = self.history_dir / "commands.jsonl"
        
        # Sensitive data patterns to filter
        self.sensitive_patterns = [
            r"password\s*=\s*['\"]?[^'\"\s]+",
            r"api[_-]?key\s*=\s*['\"]?[^'\"\s]+",
            r"token\s*=\s*['\"]?[^'\"\s]+",
            r"secret\s*=\s*['\"]?[^'\"\s]+",
            r"--password\s+[^\s]+",
            r"-p\s+[^\s]+",
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in self.sensitive_patterns
        ]
    
    async def add_command(
        self,
        command: str,
        working_directory: Optional[str] = None,
        exit_code: Optional[int] = None,
        execution_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a command to history.
        
        Args:
            command: Command that was executed
            working_directory: Directory where command was executed
            exit_code: Command exit code
            execution_time: Execution time in seconds
            metadata: Additional metadata
        """
        try:
            # Filter sensitive data
            filtered_command = self._filter_sensitive_data(command)
            
            # Create history entry
            entry = {
                "timestamp": datetime.now().isoformat(),
                "command": filtered_command,
                "original_length": len(command),
                "working_directory": working_directory,
                "exit_code": exit_code,
                "execution_time": execution_time,
                "metadata": metadata or {},
                "filtered": filtered_command != command,
            }
            
            # Append to history file
            async with aiofiles.open(self.history_file, 'a', encoding='utf-8') as f:
                await f.write(json.dumps(entry) + '\n')
            
            logger.debug(f"Added command to history: {filtered_command[:50]}...")
            
        except Exception as e:
            logger.error(f"Failed to add command to history: {e}")
    
    async def get_recent_commands(
        self,
        limit: int = 100,
        include_filtered: bool = False
    ) -> List[Dict[str, Any]]:
        """Get recent commands from history.
        
        Args:
            limit: Maximum number of commands to return
            include_filtered: Include commands that were filtered
            
        Returns:
            List of command history entries
        """
        try:
            if not self.history_file.exists():
                return []
            
            commands = []
            
            async with aiofiles.open(self.history_file, 'r', encoding='utf-8') as f:
                async for line in f:
                    if not line.strip():
                        continue
                    
                    try:
                        entry = json.loads(line)
                        
                        # Skip filtered commands if requested
                        if not include_filtered and entry.get("filtered", False):
                            continue
                        
                        commands.append(entry)
                        
                        # Stop if we have enough commands
                        if len(commands) >= limit:
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            # Return most recent first
            return list(reversed(commands[-limit:]))
            
        except Exception as e:
            logger.error(f"Failed to get recent commands: {e}")
            return []
    
    async def search_commands(
        self,
        query: str,
        limit: int = 50,
        case_sensitive: bool = False
    ) -> List[Dict[str, Any]]:
        """Search command history.
        
        Args:
            query: Search query
            limit: Maximum number of results
            case_sensitive: Whether search is case sensitive
            
        Returns:
            List of matching command entries
        """
        try:
            if not self.history_file.exists():
                return []
            
            matches = []
            search_query = query if case_sensitive else query.lower()
            
            async with aiofiles.open(self.history_file, 'r', encoding='utf-8') as f:
                async for line in f:
                    if not line.strip():
                        continue
                    
                    try:
                        entry = json.loads(line)
                        command = entry.get("command", "")
                        
                        # Search in command
                        search_text = command if case_sensitive else command.lower()
                        
                        if search_query in search_text:
                            matches.append(entry)
                            
                            if len(matches) >= limit:
                                break
                                
                    except json.JSONDecodeError:
                        continue
            
            return matches
            
        except Exception as e:
            logger.error(f"Failed to search commands: {e}")
            return []
    
    async def get_command_stats(self) -> Dict[str, Any]:
        """Get statistics about command history.
        
        Returns:
            Dictionary with history statistics
        """
        try:
            if not self.history_file.exists():
                return {
                    "total_commands": 0,
                    "filtered_commands": 0,
                    "success_rate": 0.0,
                    "most_used_commands": [],
                    "average_execution_time": 0.0,
                }
            
            total_commands = 0
            filtered_commands = 0
            successful_commands = 0
            execution_times = []
            command_counts = {}
            
            async with aiofiles.open(self.history_file, 'r', encoding='utf-8') as f:
                async for line in f:
                    if not line.strip():
                        continue
                    
                    try:
                        entry = json.loads(line)
                        total_commands += 1
                        
                        if entry.get("filtered", False):
                            filtered_commands += 1
                        
                        exit_code = entry.get("exit_code")
                        if exit_code is not None and exit_code == 0:
                            successful_commands += 1
                        
                        execution_time = entry.get("execution_time")
                        if execution_time is not None:
                            execution_times.append(execution_time)
                        
                        # Count command frequency
                        command = entry.get("command", "").split()[0] if entry.get("command") else ""
                        if command:
                            command_counts[command] = command_counts.get(command, 0) + 1
                            
                    except json.JSONDecodeError:
                        continue
            
            # Calculate statistics
            success_rate = (successful_commands / total_commands * 100) if total_commands > 0 else 0
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
            
            # Most used commands
            most_used = sorted(command_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                "total_commands": total_commands,
                "filtered_commands": filtered_commands,
                "success_rate": round(success_rate, 2),
                "most_used_commands": most_used,
                "average_execution_time": round(avg_execution_time, 3),
            }
            
        except Exception as e:
            logger.error(f"Failed to get command stats: {e}")
            return {}
    
    async def cleanup_old_history(self, days: int = 90) -> int:
        """Clean up old history entries.
        
        Args:
            days: Delete entries older than this many days
            
        Returns:
            Number of entries deleted
        """
        try:
            if not self.history_file.exists():
                return 0
            
            cutoff_date = datetime.now() - timedelta(days=days)
            temp_file = self.history_file.with_suffix('.tmp')
            
            kept_entries = 0
            deleted_entries = 0
            
            async with aiofiles.open(self.history_file, 'r', encoding='utf-8') as infile:
                async with aiofiles.open(temp_file, 'w', encoding='utf-8') as outfile:
                    async for line in infile:
                        if not line.strip():
                            continue
                        
                        try:
                            entry = json.loads(line)
                            entry_date = datetime.fromisoformat(entry["timestamp"])
                            
                            if entry_date >= cutoff_date:
                                await outfile.write(line)
                                kept_entries += 1
                            else:
                                deleted_entries += 1
                                
                        except (json.JSONDecodeError, KeyError, ValueError):
                            # Keep malformed entries
                            await outfile.write(line)
                            kept_entries += 1
            
            # Replace original file with cleaned version
            temp_file.replace(self.history_file)
            
            logger.info(f"Cleaned up {deleted_entries} old history entries, kept {kept_entries}")
            return deleted_entries
            
        except Exception as e:
            logger.error(f"Failed to cleanup old history: {e}")
            return 0
    
    def _filter_sensitive_data(self, command: str) -> str:
        """Filter sensitive data from command.
        
        Args:
            command: Original command
            
        Returns:
            Command with sensitive data filtered
        """
        filtered_command = command
        
        for pattern in self.compiled_patterns:
            filtered_command = pattern.sub("[FILTERED]", filtered_command)
        
        return filtered_command
    
    async def export_history(
        self,
        output_file: Path,
        format: str = "json",
        include_filtered: bool = False
    ) -> bool:
        """Export command history to file.
        
        Args:
            output_file: Output file path
            format: Export format (json, csv, txt)
            include_filtered: Include filtered commands
            
        Returns:
            True if successful
        """
        try:
            commands = await self.get_recent_commands(
                limit=10000,  # Large limit for export
                include_filtered=include_filtered
            )
            
            if format == "json":
                async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(commands, indent=2))
            
            elif format == "csv":
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.DictWriter(output, fieldnames=[
                    "timestamp", "command", "working_directory", 
                    "exit_code", "execution_time"
                ])
                writer.writeheader()
                
                for cmd in commands:
                    writer.writerow({
                        "timestamp": cmd.get("timestamp"),
                        "command": cmd.get("command"),
                        "working_directory": cmd.get("working_directory"),
                        "exit_code": cmd.get("exit_code"),
                        "execution_time": cmd.get("execution_time"),
                    })
                
                async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                    await f.write(output.getvalue())
            
            elif format == "txt":
                async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                    for cmd in commands:
                        timestamp = cmd.get("timestamp", "")
                        command = cmd.get("command", "")
                        await f.write(f"{timestamp}: {command}\n")
            
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Exported {len(commands)} commands to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export history: {e}")
            return False
