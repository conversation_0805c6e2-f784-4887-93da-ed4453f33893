# AI Terminal 🤖

A sophisticated AI-powered CLI terminal tool that brings advanced AI capabilities directly to your command line. Built with modern Python architecture, enterprise-grade security, and a focus on developer experience.

## ✨ Features

### 🤖 **Multi-Provider AI Support**
- **OpenAI**: GPT-4, GPT-3.5-turbo with function calling
- **Deepseek**: High-performance coding models
- **Anthropic**: Claude 3 family (Opus, Sonnet, Haiku)
- **Ollama**: Local models (Llama, CodeLlama, Mistral)
- **Azure OpenAI**: Enterprise-grade deployment

### 🎨 **Modern Terminal Interface**
- Real-time streaming AI responses
- Syntax highlighting and rich formatting
- Interactive model/provider selection
- Advanced input system with slash commands
- File tag expansion (`@file.txt`)
- Tab completion and intelligent suggestions

### 🛡️ **Enterprise-Grade Security**
- Multi-platform sandboxing (firejail, native)
- Granular command approval policies
- Secure credential management
- Audit logging and compliance features

### 💾 **Intelligent Session Management**
- Persistent conversation history
- Session resumption and browsing
- Context-aware file operations
- Automatic backup and recovery

### 🔧 **Advanced Code Assistance**
- File patching with unified diff support
- Multi-modal input processing (text + images)
- Directory-wide context analysis
- Intelligent conflict resolution

## 🚀 Quick Start

### Installation

```bash
# Run the installation script
python install.py

# Or install manually with Poetry
poetry install

# Or with pip
pip install -e .
```

### Initial Setup

Run the interactive setup wizard:

```bash
python cli.py setup
```

This will guide you through:
1. AI provider selection and API key configuration
2. Model preferences and capabilities
3. Security and approval policy settings
4. Terminal interface customization

### Basic Usage

```bash
# Start interactive chat mode
python cli.py chat

# Quick command execution
python cli.py quick "Explain this Python function" --file my_script.py

# Use specific model
python cli.py chat --model gpt-4 --provider openai

# Enable sandbox mode
python cli.py chat --sandbox
```

## 🎯 **Slash Commands**

- `/model` - Switch AI model or provider
- `/clear` - Clear conversation history
- `/compact` - Toggle compact display mode
- `/history` - Browse conversation history
- `/sessions` - Manage saved sessions
- `/help` - Show available commands
- `/exit` - Exit chat

## 🔧 **Configuration**

### Environment Variables

```bash
# AI Provider API Keys
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export DEEPSEEK_API_KEY="your-deepseek-key"

# Configuration
export AI_TERMINAL_CONFIG_PATH="~/.config/ai-terminal"
export AI_TERMINAL_LOG_LEVEL="INFO"
export AI_TERMINAL_SANDBOX_ENABLED="true"
```

### Configuration File

The setup wizard creates `~/.config/ai-terminal/config.yaml`:

```yaml
# Default provider and model
default_provider: "openai"
default_model: "gpt-4"

# Provider configurations
providers:
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    model: "gpt-4"

  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    model: "claude-3-sonnet-20240229"

  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"

# Security settings
security:
  sandbox_enabled: true
  approval_required: ["rm", "sudo", "chmod"]
  max_file_size: 10000000  # 10MB

# UI settings
ui:
  theme: "dark"
  streaming: true
  syntax_highlighting: true
  compact_mode: false
```

## 🛡️ **Security Features**

### Command Approval System
- Configurable command approval requirements
- Pattern-based risk assessment
- Interactive approval prompts
- Audit trail for all executed commands

### File Operations Security
- Path traversal protection
- File size limits
- Extension filtering
- Automatic backups

## 🧪 **Development**

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=ai_terminal

# Run specific test
python -m pytest tests/test_basic.py
```

### Code Quality

```bash
# Format code (if black is installed)
black ai_terminal/

# Type checking (if mypy is installed)
mypy ai_terminal/
```

## 📊 **Architecture**

The application follows a modular architecture:

```
ai_terminal/
├── config/          # Configuration management
├── providers/       # AI provider implementations
├── agents/          # Core AI interaction logic
├── ui/             # Terminal interface components
├── security/       # Security and approval systems
├── storage/        # Session and history management
└── utils/          # Utilities and helpers
```

### Key Components

- **Providers**: Abstracted AI provider interfaces (OpenAI, Anthropic, Ollama)
- **Agent Loop**: Core conversation management with streaming
- **Tool Registry**: Extensible tool system for function calling
- **Security Manager**: Command approval and sandboxing
- **Session Manager**: Persistent conversation storage
- **Terminal Chat**: Rich terminal interface with syntax highlighting

## 🎮 **Usage Examples**

### Interactive Chat
```bash
python cli.py chat
```

### Quick Commands
```bash
# Code review
python cli.py quick "Review this code for bugs" --file script.py

# System analysis
python cli.py quick "What's using the most CPU?" --provider ollama

# File operations
python cli.py quick "Create a backup script for my home directory"
```

### Provider Management
```bash
# List available providers
python cli.py providers

# List models for a provider
python cli.py models --provider anthropic

# Show current configuration
python cli.py config --show
```

## 🔍 **Advanced Features**

### File Tag Expansion
Include files in your messages using `@filename`:
```
You: Review this code @script.py and suggest improvements
```

### Session Management
- Conversations are automatically saved
- Resume previous sessions
- Search conversation history
- Export/import sessions

### Tool System
The AI can execute various tools:
- Shell commands (with approval)
- File operations (read, write, search)
- System information gathering
- Directory listing and navigation

## 🚨 **Security Considerations**

### Command Approval
Dangerous commands require approval:
- `rm -rf` (destructive deletion)
- `sudo` commands (elevated privileges)
- System file modifications
- Network operations

### Sandboxing
- Process isolation on supported platforms
- Restricted file system access
- Network access controls
- Resource usage limits

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`python -m pytest`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- Built with [Rich](https://rich.readthedocs.io/) for beautiful terminal output
- Powered by [Typer](https://typer.tiangolo.com/) for the CLI interface
- Uses [Pydantic](https://pydantic.dev/) for robust configuration management
- Async support with [aiofiles](https://github.com/Tinche/aiofiles) and [aiohttp](https://docs.aiohttp.org/)

## 🆘 **Troubleshooting**

### Common Issues

1. **Import errors**: Run `python install.py` to install dependencies
2. **API key errors**: Set environment variables or run `python cli.py setup`
3. **Permission errors**: Check file permissions and approval settings
4. **Provider connection issues**: Verify API keys and network connectivity

### Getting Help

- Check the logs in `~/.local/share/ai-terminal/logs/`
- Run with debug mode: `python cli.py chat --debug`
- Open an issue on GitHub with error details
