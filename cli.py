"""
Main CLI entry point for AI Terminal.

Provides command-line interface with setup wizard, interactive chat,
and various configuration options.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional, List
import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from ai_terminal.config.settings import get_settings, reload_settings
from ai_terminal.config.providers import get_provider_by_name, get_all_providers
from ai_terminal.providers.openai_client import OpenAIProvider, DeepseekProvider
from ai_terminal.providers.anthropic_client import AnthropicProvider
from ai_terminal.providers.ollama_client import OllamaProvider
from ai_terminal.agents.agent_loop import AgentLoop
from ai_terminal.agents.context import ConversationContext
from ai_terminal.agents.tools import ToolRegistry
from ai_terminal.ui.terminal_chat import TerminalChat
from ai_terminal.utils.logger import get_logger, setup_logging
from ai_terminal import __version__

# Initialize CLI app
app = typer.Typer(
    name="ai-terminal",
    help="Sophisticated AI-powered CLI terminal tool",
    add_completion=False,
)

console = Console()
logger = get_logger(__name__)


def version_callback(value: bool):
    """Show version information."""
    if value:
        console.print(f"AI Terminal v{__version__}")
        raise typer.Exit()


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", callback=version_callback, help="Show version"
    ),
    debug: bool = typer.Option(False, "--debug", help="Enable debug mode"),
    config_file: Optional[Path] = typer.Option(None, "--config", help="Configuration file path"),
):
    """AI Terminal - Sophisticated AI-powered CLI tool."""
    if debug:
        setup_logging(level="DEBUG")
    
    if config_file:
        # Load custom config file
        settings = get_settings()
        if config_file.exists():
            from ai_terminal.config.settings import Settings
            custom_settings = Settings.load_from_file(config_file)
            # Update global settings
            settings.__dict__.update(custom_settings.__dict__)


@app.command()
def chat(
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="AI provider to use"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Model to use"),
    temperature: float = typer.Option(0.7, "--temperature", "-t", help="Sampling temperature"),
    system: Optional[str] = typer.Option(None, "--system", "-s", help="System message"),
    session: Optional[str] = typer.Option(None, "--session", help="Session ID to resume"),
    no_stream: bool = typer.Option(False, "--no-stream", help="Disable streaming responses"),
    sandbox: bool = typer.Option(False, "--sandbox", help="Enable sandbox mode"),
):
    """Start interactive chat mode."""
    asyncio.run(_run_chat(
        provider=provider,
        model=model,
        temperature=temperature,
        system=system,
        session=session,
        stream=not no_stream,
        sandbox=sandbox,
    ))


@app.command()
def quick(
    message: str = typer.Argument(..., help="Message to send to AI"),
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="AI provider to use"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Model to use"),
    temperature: float = typer.Option(0.7, "--temperature", "-t", help="Sampling temperature"),
    files: Optional[List[str]] = typer.Option(None, "--file", "-f", help="Files to include"),
    no_stream: bool = typer.Option(False, "--no-stream", help="Disable streaming responses"),
):
    """Send a quick message to AI and get response."""
    asyncio.run(_run_quick(
        message=message,
        provider=provider,
        model=model,
        temperature=temperature,
        files=files or [],
        stream=not no_stream,
    ))


@app.command()
def setup():
    """Run interactive setup wizard."""
    async def run_enhanced_setup():
        from ai_terminal.setup_wizard import run_setup_wizard
        success = await run_setup_wizard()
        if success:
            console.print("[green]Setup completed successfully![/green]")
        else:
            console.print("[red]Setup was cancelled or failed[/red]")

    asyncio.run(run_enhanced_setup())


@app.command()
def providers():
    """List available AI providers and their status."""
    asyncio.run(_list_providers())


@app.command()
def models(
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="Provider to list models for"),
):
    """List available models for a provider."""
    asyncio.run(_list_models(provider))


@app.command()
def config(
    show: bool = typer.Option(False, "--show", help="Show current configuration"),
    edit: bool = typer.Option(False, "--edit", help="Edit configuration file"),
    reset: bool = typer.Option(False, "--reset", help="Reset to default configuration"),
):
    """Manage configuration."""
    if show:
        _show_config()
    elif edit:
        _edit_config()
    elif reset:
        _reset_config()
    else:
        console.print("Use --show, --edit, or --reset")


@app.command()
def sessions(
    list_sessions: bool = typer.Option(False, "--list", "-l", help="List saved sessions"),
    resume: Optional[str] = typer.Option(None, "--resume", "-r", help="Resume a session by ID"),
    delete: Optional[str] = typer.Option(None, "--delete", "-d", help="Delete a session by ID"),
    export: Optional[str] = typer.Option(None, "--export", "-e", help="Export a session to file"),
):
    """Manage conversation sessions."""
    if list_sessions:
        asyncio.run(_list_sessions())
    elif resume:
        asyncio.run(_resume_session(resume))
    elif delete:
        asyncio.run(_delete_session(delete))
    elif export:
        asyncio.run(_export_session(export))
    else:
        console.print("Use --list, --resume, --delete, or --export")


@app.command()
def health():
    """Check system health and provider connectivity."""
    asyncio.run(_check_health())


@app.command()
def demo():
    """Run feature demonstration."""
    asyncio.run(_run_demo())


async def _run_chat(
    provider: Optional[str] = None,
    model: Optional[str] = None,
    temperature: float = 0.7,
    system: Optional[str] = None,
    session: Optional[str] = None,
    stream: bool = True,
    sandbox: bool = False,
):
    """Run interactive chat mode."""
    try:
        settings = get_settings()
        
        # Initialize provider
        ai_provider = await _create_provider(provider or settings.default_provider)
        if not ai_provider:
            console.print("[red]Failed to initialize AI provider[/red]")
            return
        
        # Create agent loop
        tool_registry = ToolRegistry()
        agent = AgentLoop(
            provider=ai_provider,
            tool_registry=tool_registry,
        )
        
        # Start terminal chat UI
        chat_ui = TerminalChat(
            agent=agent,
            settings=settings,
        )
        
        await chat_ui.run(
            model=model,
            temperature=temperature,
            system_message=system,
            session_id=session,
            stream=stream,
        )
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Chat session ended[/yellow]")
    except Exception as e:
        logger.error(f"Chat failed: {e}", exc_info=True)
        console.print(f"[red]Error: {e}[/red]")
    finally:
        if 'ai_provider' in locals() and ai_provider is not None:
            await ai_provider.cleanup()


async def _run_quick(
    message: str,
    provider: Optional[str] = None,
    model: Optional[str] = None,
    temperature: float = 0.7,
    files: List[str] = None,
    stream: bool = True,
):
    """Run quick message mode."""
    try:
        settings = get_settings()
        
        # Initialize provider
        ai_provider = await _create_provider(provider or settings.default_provider)
        if not ai_provider:
            console.print("[red]Failed to initialize AI provider[/red]")
            return
        
        # Create agent loop
        tool_registry = ToolRegistry()
        agent = AgentLoop(
            provider=ai_provider,
            tool_registry=tool_registry,
        )
        
        # Start conversation
        await agent.start_conversation(
            model=model,
            temperature=temperature,
        )
        
        # Send message and display response
        console.print(f"[bold blue]You:[/bold blue] {message}")
        console.print("[bold green]AI:[/bold green]", end=" ")
        
        response_content = ""
        async for chunk in agent.send_message(message, files=files, stream=stream):
            if chunk.content:
                console.print(chunk.content, end="")
                response_content += chunk.content
            
            if chunk.is_complete:
                break
        
        console.print()  # New line after response
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted[/yellow]")
    except Exception as e:
        logger.error(f"Quick message failed: {e}", exc_info=True)
        console.print(f"[red]Error: {e}[/red]")
    finally:
        if 'ai_provider' in locals() and ai_provider is not None:
            await ai_provider.cleanup()


async def _run_setup():
    """Run interactive setup wizard."""
    console.print(Panel.fit(
        "[bold blue]AI Terminal Setup Wizard[/bold blue]\n"
        "This wizard will help you configure AI Terminal for first use.",
        title="Welcome"
    ))
    
    settings = get_settings()
    
    # Provider selection
    console.print("\n[bold]Step 1: Select AI Provider[/bold]")
    providers = get_all_providers()
    
    for i, (provider_type, config) in enumerate(providers.items(), 1):
        console.print(f"{i}. {config.display_name} - {config.base_url}")
    
    while True:
        try:
            choice = typer.prompt("Select provider (1-4)", type=int)
            if 1 <= choice <= len(providers):
                selected_provider = list(providers.keys())[choice - 1]
                break
            else:
                console.print("[red]Invalid choice[/red]")
        except ValueError:
            console.print("[red]Please enter a number[/red]")
    
    # API key configuration
    provider_config = providers[selected_provider]
    if provider_config.api_key_env:
        console.print(f"\n[bold]Step 2: Configure API Key for {provider_config.display_name}[/bold]")
        api_key = typer.prompt(f"Enter your {provider_config.display_name} API key", hide_input=True)
        
        # Save API key to environment or config
        import os
        os.environ[provider_config.api_key_env] = api_key
        console.print("[green]API key configured[/green]")
    
    # Model selection
    console.print(f"\n[bold]Step 3: Select Default Model[/bold]")
    try:
        ai_provider = await _create_provider(selected_provider.value)
        if ai_provider:
            models = await ai_provider.list_models()
            if models:
                for i, model in enumerate(models[:10], 1):  # Show first 10 models
                    console.print(f"{i}. {model}")
                
                while True:
                    try:
                        choice = typer.prompt("Select model (1-10)", type=int)
                        if 1 <= choice <= len(models):
                            selected_model = models[choice - 1]
                            break
                        else:
                            console.print("[red]Invalid choice[/red]")
                    except ValueError:
                        console.print("[red]Please enter a number[/red]")
            else:
                selected_model = provider_config.default_model
            
            await ai_provider.cleanup()
        else:
            selected_model = provider_config.default_model
    except Exception as e:
        logger.warning(f"Failed to fetch models: {e}")
        selected_model = provider_config.default_model
    
    # Save configuration
    settings.default_provider = selected_provider.value
    settings.default_model = selected_model
    settings.save_to_file()
    
    console.print(Panel.fit(
        f"[green]Setup completed successfully![/green]\n\n"
        f"Provider: {provider_config.display_name}\n"
        f"Model: {selected_model}\n\n"
        f"You can now use 'ai-terminal chat' to start chatting!",
        title="Setup Complete"
    ))


async def _list_providers():
    """List available providers and their status."""
    console.print("[bold]Available AI Providers:[/bold]\n")
    
    providers = get_all_providers()
    
    for provider_type, config in providers.items():
        status_text = "[yellow]Unknown[/yellow]"
        
        try:
            provider = await _create_provider(provider_type.value)
            if provider:
                is_valid = await provider.validate_connection()
                status_text = "[green]Available[/green]" if is_valid else "[red]Unavailable[/red]"
                await provider.cleanup()
        except Exception:
            status_text = "[red]Error[/red]"
        
        console.print(f"• {config.display_name} ({provider_type.value}) - {status_text}")
        console.print(f"  URL: {config.base_url}")
        console.print(f"  Models: {len(config.models)} available")
        console.print()


async def _list_models(provider_name: Optional[str] = None):
    """List available models."""
    settings = get_settings()
    provider_name = provider_name or settings.default_provider
    
    console.print(f"[bold]Models for {provider_name}:[/bold]\n")
    
    try:
        provider = await _create_provider(provider_name)
        if provider:
            models = await provider.list_models()
            for model in models:
                console.print(f"• {model}")
            await provider.cleanup()
        else:
            console.print("[red]Failed to connect to provider[/red]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")


async def _create_provider(provider_name: str):
    """Create and initialize a provider instance."""
    settings = get_settings()
    
    try:
        if provider_name == "openai":
            api_key = settings.get_api_key("openai")
            if not api_key:
                console.print("[red]OpenAI API key not found. Set OPENAI_API_KEY environment variable.[/red]")
                return None
            provider = OpenAIProvider(api_key=api_key)
        
        elif provider_name == "deepseek":
            api_key = settings.get_api_key("deepseek")
            if not api_key:
                console.print("[red]Deepseek API key not found. Set DEEPSEEK_API_KEY environment variable.[/red]")
                return None
            provider = DeepseekProvider(api_key=api_key)
        
        elif provider_name == "anthropic":
            api_key = settings.get_api_key("anthropic")
            if not api_key:
                console.print("[red]Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.[/red]")
                return None
            provider = AnthropicProvider(api_key=api_key)
        
        elif provider_name == "ollama":
            provider = OllamaProvider()
        
        else:
            console.print(f"[red]Unknown provider: {provider_name}[/red]")
            return None
        
        await provider.initialize()
        return provider
        
    except Exception as e:
        logger.error(f"Failed to create provider {provider_name}: {e}")
        console.print(f"[red]Failed to initialize {provider_name}: {e}[/red]")
        return None


def _show_config():
    """Show current configuration."""
    settings = get_settings()
    
    console.print("[bold]Current Configuration:[/bold]\n")
    console.print(f"Default Provider: {settings.default_provider}")
    console.print(f"Default Model: {settings.default_model}")
    console.print(f"Config Directory: {settings.config_dir}")
    console.print(f"Data Directory: {settings.data_dir}")
    console.print(f"Debug Mode: {settings.debug}")
    console.print(f"Sandbox Enabled: {settings.security.sandbox_enabled}")


def _edit_config():
    """Edit configuration file."""
    settings = get_settings()
    config_file = settings.config_dir / "config.yaml"
    
    if not config_file.exists():
        settings.save_to_file(config_file)
    
    import subprocess
    import os
    
    editor = os.environ.get("EDITOR", "notepad" if sys.platform == "win32" else "nano")
    subprocess.run([editor, str(config_file)])


def _reset_config():
    """Reset configuration to defaults."""
    if typer.confirm("Are you sure you want to reset configuration to defaults?"):
        settings = get_settings()
        config_file = settings.config_dir / "config.yaml"
        if config_file.exists():
            config_file.unlink()
        console.print("[green]Configuration reset to defaults[/green]")


async def _list_sessions():
    """List saved conversation sessions."""
    try:
        from ai_terminal.storage.sessions import SessionManager

        session_manager = SessionManager()
        sessions = await session_manager.list_sessions()

        if not sessions:
            console.print("[yellow]No saved sessions found[/yellow]")
            return

        console.print("[bold]Saved Sessions:[/bold]\n")

        from rich.table import Table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Session ID", style="cyan")
        table.add_column("Created", style="white")
        table.add_column("Messages", style="dim")
        table.add_column("Provider", style="green")
        table.add_column("Model", style="blue")

        for session in sessions:
            session_id = session.get("session_id", "")[:8] + "..."
            created = session.get("created_at", "Unknown")
            message_count = session.get("message_count", 0)
            provider = session.get("provider", "Unknown")
            model = session.get("model", "Unknown")

            table.add_row(session_id, created, str(message_count), provider, model)

        console.print(table)

    except Exception as e:
        logger.error(f"Failed to list sessions: {e}")
        console.print(f"[red]Error: {e}[/red]")


async def _resume_session(session_id: str):
    """Resume a conversation session."""
    console.print(f"[yellow]Resuming session {session_id}...[/yellow]")
    # This would integrate with the chat command
    await _run_chat(session=session_id)


async def _delete_session(session_id: str):
    """Delete a conversation session."""
    try:
        from ai_terminal.storage.sessions import SessionManager

        if typer.confirm(f"Are you sure you want to delete session {session_id}?"):
            session_manager = SessionManager()
            await session_manager.delete_session(session_id)
            console.print(f"[green]Session {session_id} deleted[/green]")

    except Exception as e:
        logger.error(f"Failed to delete session: {e}")
        console.print(f"[red]Error: {e}[/red]")


async def _export_session(session_id: str):
    """Export a conversation session."""
    try:
        from ai_terminal.storage.sessions import SessionManager

        session_manager = SessionManager()
        session_data = await session_manager.get_session(session_id)

        if not session_data:
            console.print(f"[red]Session {session_id} not found[/red]")
            return

        # Export to JSON file
        import json
        from datetime import datetime

        filename = f"session_{session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(filename, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)

        console.print(f"[green]Session exported to {filename}[/green]")

    except Exception as e:
        logger.error(f"Failed to export session: {e}")
        console.print(f"[red]Error: {e}[/red]")


async def _check_health():
    """Check system health and connectivity."""
    console.print("[bold]AI Terminal Health Check[/bold]\n")

    # Check providers
    console.print("[bold]Provider Connectivity:[/bold]")
    providers = get_all_providers()

    for provider_type, config in providers.items():
        try:
            provider = await _create_provider(provider_type.value)
            if provider:
                # Test basic connectivity
                models = await provider.list_models()
                status = f"[green]✅ OK ({len(models)} models)[/green]"
                await provider.cleanup()
            else:
                status = "[red]❌ Failed to initialize[/red]"
        except Exception as e:
            status = f"[red]❌ Error: {str(e)[:50]}...[/red]"

        console.print(f"  {config.display_name}: {status}")

    # Check system resources
    console.print("\n[bold]System Resources:[/bold]")
    try:
        import psutil

        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        console.print(f"  CPU Usage: {cpu_percent}%")
        console.print(f"  Memory Usage: {memory.percent}% ({memory.used // 1024**3}GB / {memory.total // 1024**3}GB)")
        console.print(f"  Disk Usage: {disk.percent}% ({disk.used // 1024**3}GB / {disk.total // 1024**3}GB)")

    except ImportError:
        console.print("  [yellow]psutil not available - install for system monitoring[/yellow]")
    except Exception as e:
        console.print(f"  [red]Error checking system resources: {e}[/red]")

    # Check configuration
    console.print("\n[bold]Configuration:[/bold]")
    settings = get_settings()
    console.print(f"  Config Directory: {settings.config_dir}")
    console.print(f"  Data Directory: {settings.data_dir}")
    console.print(f"  Default Provider: {settings.default_provider}")
    console.print(f"  Sandbox Enabled: {settings.security.sandbox_enabled}")


async def _run_demo():
    """Run feature demonstration."""
    console.print("[bold blue]Starting AI Terminal Demo...[/bold blue]\n")

    try:
        # Import and run the demo
        from examples.demo import main as demo_main
        await demo_main()

    except ImportError:
        console.print("[red]Demo module not found[/red]")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        console.print(f"[red]Demo failed: {e}[/red]")


def main():
    """Main entry point."""
    app()


if __name__ == "__main__":
    main()
