"""
Cross-platform notification system.

Provides desktop notifications for important events and status updates
with platform-specific implementations.
"""

import platform
import asyncio
from typing import Optional
from pathlib import Path

from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class NotificationManager:
    """Cross-platform notification manager."""
    
    def __init__(self):
        """Initialize notification manager."""
        self.system = platform.system()
        self.enabled = True
        
        # Try to import platform-specific modules
        self._setup_platform_support()
    
    def _setup_platform_support(self):
        """Setup platform-specific notification support."""
        if self.system == "Windows":
            try:
                import win10toast
                self._win_toaster = win10toast.ToastNotifier()
                logger.debug("Windows toast notifications available")
            except ImportError:
                logger.warning("win10toast not available, notifications disabled")
                self.enabled = False
        
        elif self.system == "Darwin":  # macOS
            # macOS uses osascript, which should be available by default
            logger.debug("macOS notifications available via osascript")
        
        elif self.system == "Linux":
            # Linux uses notify-send, check if available
            import shutil
            if shutil.which("notify-send"):
                logger.debug("Linux notifications available via notify-send")
            else:
                logger.warning("notify-send not found, notifications disabled")
                self.enabled = False
        
        else:
            logger.warning(f"Notifications not supported on {self.system}")
            self.enabled = False
    
    async def send_notification(
        self,
        title: str,
        message: str,
        icon: Optional[str] = None,
        duration: int = 5000,
        urgency: str = "normal"
    ) -> bool:
        """Send a desktop notification.
        
        Args:
            title: Notification title
            message: Notification message
            icon: Path to icon file (optional)
            duration: Duration in milliseconds
            urgency: Urgency level (low, normal, critical)
            
        Returns:
            True if notification was sent successfully
        """
        if not self.enabled:
            logger.debug(f"Notifications disabled: {title} - {message}")
            return False
        
        try:
            if self.system == "Windows":
                return await self._send_windows_notification(title, message, icon, duration)
            elif self.system == "Darwin":
                return await self._send_macos_notification(title, message, icon)
            elif self.system == "Linux":
                return await self._send_linux_notification(title, message, icon, duration, urgency)
            else:
                return False
                
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return False
    
    async def _send_windows_notification(
        self,
        title: str,
        message: str,
        icon: Optional[str] = None,
        duration: int = 5000
    ) -> bool:
        """Send Windows toast notification."""
        try:
            # Run in thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            def show_toast():
                self._win_toaster.show_toast(
                    title=title,
                    msg=message,
                    icon_path=icon,
                    duration=duration // 1000,  # Convert to seconds
                    threaded=True
                )
            
            await loop.run_in_executor(None, show_toast)
            logger.debug(f"Sent Windows notification: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Windows notification failed: {e}")
            return False
    
    async def _send_macos_notification(
        self,
        title: str,
        message: str,
        icon: Optional[str] = None
    ) -> bool:
        """Send macOS notification using osascript."""
        try:
            # Build osascript command
            script = f'''
            display notification "{message}" with title "{title}"
            '''
            
            # Execute osascript
            process = await asyncio.create_subprocess_exec(
                "osascript", "-e", script,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.debug(f"Sent macOS notification: {title}")
                return True
            else:
                logger.error(f"osascript failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"macOS notification failed: {e}")
            return False
    
    async def _send_linux_notification(
        self,
        title: str,
        message: str,
        icon: Optional[str] = None,
        duration: int = 5000,
        urgency: str = "normal"
    ) -> bool:
        """Send Linux notification using notify-send."""
        try:
            # Build notify-send command
            cmd = ["notify-send"]
            
            # Add options
            cmd.extend(["-t", str(duration)])
            cmd.extend(["-u", urgency])
            
            if icon:
                cmd.extend(["-i", icon])
            
            # Add title and message
            cmd.extend([title, message])
            
            # Execute notify-send
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.debug(f"Sent Linux notification: {title}")
                return True
            else:
                logger.error(f"notify-send failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Linux notification failed: {e}")
            return False
    
    async def notify_ai_response_ready(self, session_id: str) -> bool:
        """Notify that an AI response is ready."""
        return await self.send_notification(
            title="AI Terminal",
            message=f"Response ready for session {session_id[:8]}...",
            urgency="low"
        )
    
    async def notify_tool_execution(self, tool_name: str, success: bool) -> bool:
        """Notify about tool execution result."""
        status = "completed" if success else "failed"
        urgency = "normal" if success else "critical"
        
        return await self.send_notification(
            title="AI Terminal - Tool Execution",
            message=f"Tool '{tool_name}' {status}",
            urgency=urgency
        )
    
    async def notify_error(self, error_message: str) -> bool:
        """Notify about an error."""
        return await self.send_notification(
            title="AI Terminal - Error",
            message=error_message,
            urgency="critical"
        )
    
    async def notify_session_saved(self, session_id: str) -> bool:
        """Notify that a session was saved."""
        return await self.send_notification(
            title="AI Terminal",
            message=f"Session {session_id[:8]}... saved",
            urgency="low"
        )
    
    def disable_notifications(self):
        """Disable notifications."""
        self.enabled = False
        logger.info("Notifications disabled")
    
    def enable_notifications(self):
        """Enable notifications."""
        if self.system in ["Windows", "Darwin", "Linux"]:
            self.enabled = True
            logger.info("Notifications enabled")
        else:
            logger.warning("Cannot enable notifications on unsupported platform")
    
    def is_supported(self) -> bool:
        """Check if notifications are supported on this platform."""
        return self.system in ["Windows", "Darwin", "Linux"]
    
    def is_enabled(self) -> bool:
        """Check if notifications are enabled."""
        return self.enabled
