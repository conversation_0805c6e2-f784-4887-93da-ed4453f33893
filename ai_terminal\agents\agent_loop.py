"""
Core agent loop for AI interactions.

Handles conversation management, tool execution, and streaming responses
with support for multiple AI providers and advanced features.
"""

import asyncio
import json
import platform
import hashlib
import time
from typing import Dict, List, Optional, Any, AsyncIterator, Callable, Set, Tuple
from datetime import datetime, timedelta
import uuid
from collections import defaultdict, deque

from ai_terminal.providers.base import (
    BaseProvider,
    Message,
    MessageRole,
    StreamChunk,
    CompletionResponse,
    ProviderError,
)
from ai_terminal.agents.context import ConversationContext
from ai_terminal.agents.tools import ToolRegistry, ToolResult
from ai_terminal.security.approval import ApprovalManager
from ai_terminal.utils.logger import get_logger, log_performance
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class ContextMemoryManager:
    """Advanced context and memory management for AI conversations."""

    def __init__(self, max_memory_items: int = 100, max_context_age_hours: int = 24):
        """Initialize the context memory manager.

        Args:
            max_memory_items: Maximum number of memory items to retain
            max_context_age_hours: Maximum age of context items in hours
        """
        self.max_memory_items = max_memory_items
        self.max_context_age = timedelta(hours=max_context_age_hours)

        # Memory storage
        self.conversation_patterns: Dict[str, int] = defaultdict(int)
        self.user_preferences: Dict[str, Any] = {}
        self.tool_usage_patterns: Dict[str, List[datetime]] = defaultdict(list)
        self.error_patterns: Dict[str, int] = defaultdict(int)
        self.successful_solutions: Dict[str, str] = {}

        # Context optimization
        self.important_messages: deque = deque(maxlen=50)
        self.context_summaries: Dict[str, str] = {}
        self.topic_transitions: List[Tuple[datetime, str, str]] = []

    def analyze_conversation_patterns(self, messages: List[Message]) -> Dict[str, Any]:
        """Analyze conversation patterns to optimize context."""
        patterns = {
            "dominant_topics": [],
            "tool_usage_frequency": {},
            "response_complexity": "medium",
            "user_expertise_level": "intermediate",
            "preferred_communication_style": "technical"
        }

        # Analyze message content for patterns
        tool_mentions = defaultdict(int)
        technical_terms = 0
        total_words = 0

        for message in messages[-20:]:  # Analyze recent messages
            if message.role == MessageRole.USER:
                words = message.content.split()
                total_words += len(words)

                # Count technical terms
                technical_indicators = ['function', 'class', 'method', 'variable', 'error', 'debug', 'code']
                technical_terms += sum(1 for word in words if any(term in word.lower() for term in technical_indicators))

                # Track tool mentions
                for tool_name in ['execute', 'read', 'write', 'list', 'search', 'system']:
                    if tool_name in message.content.lower():
                        tool_mentions[tool_name] += 1

        # Determine expertise level
        if total_words > 0:
            technical_ratio = technical_terms / total_words
            if technical_ratio > 0.1:
                patterns["user_expertise_level"] = "advanced"
            elif technical_ratio < 0.05:
                patterns["user_expertise_level"] = "beginner"

        patterns["tool_usage_frequency"] = dict(tool_mentions)
        return patterns

    def optimize_context_for_provider(self, messages: List[Message], provider_capabilities: Any) -> List[Message]:
        """Optimize message context based on provider capabilities."""
        max_context = provider_capabilities.max_context_length

        if not messages:
            return messages

        # Always keep system message
        system_messages = [m for m in messages if m.role == MessageRole.SYSTEM]
        other_messages = [m for m in messages if m.role != MessageRole.SYSTEM]

        # Estimate token usage (rough approximation)
        def estimate_tokens(msg: Message) -> int:
            return len(msg.content.split()) * 1.3  # Rough token estimation

        # Keep recent messages and important ones
        optimized_messages = system_messages.copy()
        current_tokens = sum(estimate_tokens(m) for m in system_messages)

        # Add messages in reverse order (most recent first)
        for message in reversed(other_messages):
            msg_tokens = estimate_tokens(message)
            if current_tokens + msg_tokens < max_context * 0.8:  # Leave 20% buffer
                optimized_messages.insert(-len(system_messages) if system_messages else 0, message)
                current_tokens += msg_tokens
            else:
                break

        return optimized_messages

    def extract_key_insights(self, messages: List[Message]) -> List[str]:
        """Extract key insights from conversation for context enhancement."""
        insights = []

        # Look for user preferences
        for message in messages:
            if message.role == MessageRole.USER:
                content = message.content.lower()

                # Detect preferences
                if "prefer" in content or "like" in content:
                    insights.append(f"User preference detected: {message.content[:100]}...")

                # Detect expertise areas
                if any(term in content for term in ["expert in", "familiar with", "experienced with"]):
                    insights.append(f"User expertise: {message.content[:100]}...")

                # Detect workflow patterns
                if any(term in content for term in ["always", "usually", "typically", "workflow"]):
                    insights.append(f"User workflow: {message.content[:100]}...")

        return insights[-10:]  # Keep last 10 insights


class ResponseCache:
    """Intelligent response caching system."""

    def __init__(self, max_cache_size: int = 1000, cache_ttl_hours: int = 24):
        """Initialize the response cache.

        Args:
            max_cache_size: Maximum number of cached responses
            cache_ttl_hours: Time-to-live for cached responses in hours
        """
        self.max_cache_size = max_cache_size
        self.cache_ttl = timedelta(hours=cache_ttl_hours)
        self.cache: Dict[str, Tuple[str, datetime, Dict[str, Any]]] = {}
        self.access_times: Dict[str, datetime] = {}

    def _generate_cache_key(self, messages: List[Message], model: str, temperature: float) -> str:
        """Generate a cache key for the given parameters."""
        # Create a hash of the conversation context
        content_hash = hashlib.md5()

        # Include recent messages (last 5) for context
        recent_messages = messages[-5:] if len(messages) > 5 else messages
        for message in recent_messages:
            content_hash.update(f"{message.role.value}:{message.content}".encode())

        content_hash.update(f"model:{model}:temp:{temperature}".encode())
        return content_hash.hexdigest()

    def get_cached_response(self, messages: List[Message], model: str, temperature: float) -> Optional[str]:
        """Get cached response if available and valid."""
        cache_key = self._generate_cache_key(messages, model, temperature)

        if cache_key in self.cache:
            response, timestamp, metadata = self.cache[cache_key]

            # Check if cache is still valid
            if datetime.now() - timestamp < self.cache_ttl:
                self.access_times[cache_key] = datetime.now()
                logger.debug(f"Cache hit for key: {cache_key[:8]}...")
                return response
            else:
                # Remove expired cache entry
                del self.cache[cache_key]
                self.access_times.pop(cache_key, None)

        return None

    def cache_response(self, messages: List[Message], model: str, temperature: float, response: str, metadata: Dict[str, Any] = None):
        """Cache a response."""
        cache_key = self._generate_cache_key(messages, model, temperature)

        # Clean up old entries if cache is full
        if len(self.cache) >= self.max_cache_size:
            self._cleanup_cache()

        self.cache[cache_key] = (response, datetime.now(), metadata or {})
        self.access_times[cache_key] = datetime.now()
        logger.debug(f"Cached response for key: {cache_key[:8]}...")

    def _cleanup_cache(self):
        """Remove old and least recently used cache entries."""
        # Remove expired entries first
        current_time = datetime.now()
        expired_keys = [
            key for key, (_, timestamp, _) in self.cache.items()
            if current_time - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            del self.cache[key]
            self.access_times.pop(key, None)

        # If still over limit, remove least recently used
        if len(self.cache) >= self.max_cache_size:
            # Sort by access time and remove oldest
            sorted_keys = sorted(self.access_times.items(), key=lambda x: x[1])
            keys_to_remove = sorted_keys[:len(self.cache) - self.max_cache_size + 10]

            for key, _ in keys_to_remove:
                self.cache.pop(key, None)
                self.access_times.pop(key, None)


class SystemPromptGenerator:
    """Generates sophisticated, context-aware system prompts for AI interactions."""

    def __init__(
        self,
        provider: BaseProvider,
        tool_registry: ToolRegistry,
        approval_manager: Optional[ApprovalManager] = None,
        settings: Optional[Any] = None,
        context_manager: Optional[ContextMemoryManager] = None
    ):
        """Initialize the system prompt generator.

        Args:
            provider: AI provider instance
            tool_registry: Tool registry for available tools
            approval_manager: Approval manager for security context
            settings: Application settings
            context_manager: Context memory manager for adaptive prompts
        """
        self.provider = provider
        self.tool_registry = tool_registry
        self.approval_manager = approval_manager
        self.settings = settings or get_settings()
        self.context_manager = context_manager

    def generate_system_prompt(
        self,
        user_system_message: Optional[str] = None,
        conversation_context: Optional[List[Message]] = None
    ) -> str:
        """Generate a comprehensive system prompt.

        Args:
            user_system_message: Optional user-provided system message
            conversation_context: Optional conversation context for adaptive prompts

        Returns:
            Complete system prompt string
        """
        prompt_sections = []

        # Core identity and role
        prompt_sections.append(self._generate_identity_section())

        # Environment and capabilities
        prompt_sections.append(self._generate_environment_section())

        # Adaptive context section (if context manager available)
        if self.context_manager and conversation_context:
            prompt_sections.append(self._generate_adaptive_context_section(conversation_context))

        # Tool system instructions
        if self.tool_registry and self.tool_registry.tools:
            prompt_sections.append(self._generate_tools_section())

        # Security and approval guidelines
        if self.approval_manager:
            prompt_sections.append(self._generate_security_section())

        # Response formatting guidelines
        prompt_sections.append(self._generate_formatting_section())

        # Error handling instructions
        prompt_sections.append(self._generate_error_handling_section())

        # Provider-specific optimizations
        prompt_sections.append(self._generate_provider_specific_section())

        # User's custom system message (if provided)
        if user_system_message:
            prompt_sections.append(f"\n## Additional Instructions\n{user_system_message}")

        return "\n\n".join(prompt_sections)

    def _generate_adaptive_context_section(self, messages: List[Message]) -> str:
        """Generate adaptive context section based on conversation patterns."""
        if not self.context_manager:
            return ""

        patterns = self.context_manager.analyze_conversation_patterns(messages)
        insights = self.context_manager.extract_key_insights(messages)

        context_section = "## Adaptive Context\n\n"

        # User expertise level adaptation
        expertise_level = patterns.get("user_expertise_level", "intermediate")
        if expertise_level == "beginner":
            context_section += "**User Profile**: Beginner level - Provide detailed explanations, avoid jargon, include step-by-step instructions.\n"
        elif expertise_level == "advanced":
            context_section += "**User Profile**: Advanced level - Use technical terminology, provide concise explanations, focus on efficiency.\n"
        else:
            context_section += "**User Profile**: Intermediate level - Balance technical detail with clear explanations.\n"

        # Tool usage patterns
        tool_usage = patterns.get("tool_usage_frequency", {})
        if tool_usage:
            most_used_tools = sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:3]
            context_section += f"**Preferred Tools**: User frequently uses: {', '.join([tool for tool, _ in most_used_tools])}\n"

        # Key insights
        if insights:
            context_section += "**Key Insights**:\n"
            for insight in insights[-5:]:  # Last 5 insights
                context_section += f"- {insight}\n"

        return context_section

    def _generate_identity_section(self) -> str:
        """Generate the AI identity and role section."""
        return """# AI Terminal Assistant

You are an advanced AI assistant integrated into AI Terminal, a sophisticated command-line interface tool. Your primary role is to help users with various tasks through intelligent conversation and tool execution.

## Core Capabilities
- Execute system commands and file operations securely
- Interact with users through natural language conversation
- Use available tools for task execution 
- Communicate effectively with users 
- Provide clear, structured responses
- Follow user instructions and requests diligently
- Ensure transparency and honesty in your interactions
- Be transparent about your capabilities and limitations
- Analyze code, files, and system information for analysis and recommendations 
- Provide technical assistance and problem-solving 
- Offer suggestions and recommendations based on analysis
- Learn from user interactions and improve over time
- Continuously optimize your responses for better performance
- Be creative and proactive in finding solutions and you can use all the tools you have access to 
- Be empathetic and understanding in your interactions
- Maintain conversation context and session history
- Be consistent in your responses and behavior
- Adapt responses based on user preferences and system capabilities


## Interaction Principles
- You are an AI assistant integrated into AI Terminal, a sophisticated command-line interface tool. Your primary role is to help users with various tasks through intelligent conversation and tool execution.
- Be helpful, accurate, and efficient in your responses
- Always prioritize user safety and system security
- Provide clear explanations for your actions and recommendations
- Ask for clarification when requests are ambiguous
- Respect user privacy and data security"""

    def _generate_environment_section(self) -> str:
        """Generate environment and system context section."""
        system_info = platform.system()
        python_version = platform.python_version()

        capabilities = self.provider.capabilities
        provider_features = []

        if capabilities.supports_streaming:
            provider_features.append("streaming responses")
        if capabilities.supports_functions:
            provider_features.append("function calling")
        if capabilities.supports_tools:
            provider_features.append("tool execution")
        if capabilities.supports_vision:
            provider_features.append("image analysis")

        features_text = ", ".join(provider_features) if provider_features else "basic text generation"

        return f"""## Environment Context

**System Information:**
- Operating System: {system_info}
- Python Version: {python_version}
- AI Provider: {self.provider.name}
- Provider Capabilities: {features_text}
- Context Length: {capabilities.max_context_length:,} tokens
- Max Output: {capabilities.max_output_tokens:,} tokens

**AI Terminal Features:**
- Secure command execution with approval system
- File operations with safety checks
- Session persistence and conversation history
- Multi-provider AI support
- Rich terminal interface with syntax highlighting"""

    def _generate_tools_section(self) -> str:
        """Generate tool system instructions section."""
        tools_info = []

        for tool_name, tool_def in self.tool_registry.tools.items():
            # Extract parameter info
            params = tool_def.parameters.get("properties", {})
            required_params = tool_def.parameters.get("required", [])

            param_descriptions = []
            for param_name, param_info in params.items():
                param_type = param_info.get("type", "string")
                param_desc = param_info.get("description", "")
                required_marker = " (required)" if param_name in required_params else " (optional)"
                param_descriptions.append(f"  - {param_name} ({param_type}){required_marker}: {param_desc}")

            params_text = "\n".join(param_descriptions) if param_descriptions else "  - No parameters required"

            tools_info.append(f"""**{tool_name}**
- Description: {tool_def.description}
- Parameters:
{params_text}""")

        tools_list = "\n\n".join(tools_info)

        return f"""## Available Tools

You have access to the following tools for executing tasks. Always use the appropriate tool when the user's request requires system interaction:

{tools_list}

### Tool Usage Guidelines
1. **Always explain** what you're going to do before using a tool
2. **Use tools judiciously** - only when necessary to fulfill the user's request and you can use all the tools you have access to and you can use them in any way you want and you can chain multiple tools together to fulfill the user's request and to solve complex problems and tasks and you can use the tools in any order you want.
3. **Handle errors gracefully** - if a tool fails, explain what happened and suggest alternatives
4. **Respect security policies** - some operations may require user approval
5. **Provide context** - explain the results and their relevance to the user's request
6. **Be transparent** - let the user know when you're using a tool and what you're doing

### Tool Execution Flow
- Tool calls are automatically executed by the system 
- Results are returned and integrated into the conversation
- Security-sensitive operations may prompt for user approval
- Failed operations will return error information for troubleshooting"""

    def _generate_security_section(self) -> str:
        """Generate security and approval guidelines section."""
        security_enabled = self.settings.security.sandbox_enabled
        approval_commands = self.settings.security.approval_required

        approval_list = ", ".join(approval_commands) if approval_commands else "none configured"

        return f"""## Security and Approval System

**Security Status:** {'Enabled' if security_enabled else 'Disabled'}

### Security Guidelines
1. **Command Execution**: All system commands are subject to security review
2. **File Operations**: File access is restricted to safe locations and operations
3. **Approval Required**: Certain operations require explicit user approval
4. **Sandboxing**: Commands may be executed in a restricted environment

### Commands Requiring Approval
The following commands typically require user approval: {approval_list}

### Security Best Practices
- **Explain risks** when suggesting potentially dangerous operations
- **Offer alternatives** for high-risk commands
- **Respect denials** if the user or system blocks an operation
- **Use least privilege** - request only the minimum permissions needed
- **Validate inputs** before executing commands with user-provided data

### When Operations Are Blocked
If a tool execution is denied:
1. Explain why the operation was blocked
2. Suggest safer alternatives if available
3. Ask the user if they want to modify the approach
4. Provide manual instructions if automated execution isn't possible"""

    def _generate_formatting_section(self) -> str:
        """Generate response formatting guidelines section."""
        return """## Response Formatting Guidelines

### General Formatting
- Use **clear, concise language** appropriate for technical users
- **Structure responses** with headers, lists, and code blocks for readability
- **Highlight important information** using markdown formatting
- **Provide examples** when explaining complex concepts or commands

### Code and Command Formatting
- Use `inline code` formatting for commands, file names, and short code snippets
- Use code blocks with language specification for longer code examples:
  ```bash
  # Example command
  ls -la /home/<USER>
  ```
- **Always explain** what commands do before or after showing them
- **Include expected output** when helpful for understanding

### Tool Usage Communication
- **Announce tool usage**: "I'll check the system information for you..."
- **Explain tool results**: "The command completed successfully and shows..."
- **Handle errors transparently**: "The operation failed because..."

### Progress and Status Updates
- For long-running operations, provide status updates
- Explain what you're doing and why
- Set appropriate expectations for response times"""

    def _generate_error_handling_section(self) -> str:
        """Generate error handling instructions section."""
        return """## Error Handling and Recovery

### When Tools Fail
1. **Acknowledge the failure** clearly and immediately
2. **Explain the likely cause** based on the error message
3. **Suggest specific solutions** or alternative approaches
4. **Offer to try again** with modified parameters if appropriate

### Common Error Scenarios
- **Permission denied**: Explain the security restriction and suggest alternatives
- **File not found**: Verify the path and suggest corrections
- **Command not found**: Check if the tool is installed or suggest alternatives
- **Network issues**: Explain connectivity problems and retry options
- **Timeout errors**: Suggest breaking down large operations

### Troubleshooting Approach
1. **Gather information** about the error context
2. **Analyze the root cause** systematically
3. **Provide step-by-step solutions** when possible
4. **Escalate to manual intervention** when automated solutions aren't available

### User Communication During Errors
- Be honest about limitations and failures
- Provide actionable next steps
- Ask clarifying questions when the error is ambiguous
- Offer to help with manual alternatives"""

    def _generate_provider_specific_section(self) -> str:
        """Generate provider-specific optimization instructions."""
        provider_name = self.provider.name.lower()
        capabilities = self.provider.capabilities

        # Provider-specific optimizations
        if provider_name in ["openai", "deepseek"]:
            optimization_tips = """
- Leverage function calling capabilities for complex tool interactions
- Use structured responses when appropriate
- Take advantage of large context windows for comprehensive analysis"""
        elif provider_name == "anthropic":
            optimization_tips = """
- Utilize Claude's strong reasoning capabilities for complex problem-solving
- Leverage the large context window for document analysis
- Use structured thinking for multi-step processes"""
        elif provider_name == "ollama":
            optimization_tips = """
- Be mindful of local processing limitations
- Optimize for efficiency and conciseness
- Consider that function calling may be limited"""
        else:
            optimization_tips = """
- Adapt responses to provider capabilities
- Use available features efficiently
- Provide clear, structured responses"""

        return f"""## Provider-Specific Optimizations

**Current Provider:** {self.provider.name}
**Capabilities:** Function calling: {capabilities.supports_functions}, Streaming: {capabilities.supports_streaming}, Vision: {capabilities.supports_vision}

### Optimization Guidelines
{optimization_tips}

### Context Management
- **Context Length:** {capabilities.max_context_length:,} tokens available
- **Output Limit:** {capabilities.max_output_tokens:,} tokens maximum
- **Memory Management:** Prioritize recent and relevant information
- **Efficiency:** Balance detail with conciseness based on context limits

### Response Strategy
- Adapt complexity to provider capabilities
- Use available features (streaming, functions, etc.) effectively
- Optimize for the specific strengths of {self.provider.name}"""


class AgentLoop:
    """Core agent loop for AI interactions."""

    def __init__(
        self,
        provider: BaseProvider,
        tool_registry: Optional[ToolRegistry] = None,
        approval_manager: Optional[ApprovalManager] = None,
        session_manager: Optional[Any] = None,  # Use Any to avoid circular import
    ):
        """Initialize the agent loop.
        
        Args:
            provider: AI provider instance
            tool_registry: Tool registry for function calling
            approval_manager: Approval manager for security
            session_manager: Session manager for persistence
        """
        self.provider = provider
        self.tool_registry = tool_registry or ToolRegistry()
        self.approval_manager = approval_manager
        self.session_manager = session_manager
        self.settings = get_settings()

        # Initialize advanced features
        self.context_manager = ContextMemoryManager()
        self.response_cache = ResponseCache()

        # Initialize system prompt generator with context manager
        self.system_prompt_generator = SystemPromptGenerator(
            provider=self.provider,
            tool_registry=self.tool_registry,
            approval_manager=self.approval_manager,
            settings=self.settings,
            context_manager=self.context_manager
        )

        # Current conversation context
        self.context: Optional[ConversationContext] = None
        
        # Event callbacks
        self.on_message_start: Optional[Callable[[Message], None]] = None
        self.on_message_chunk: Optional[Callable[[str], None]] = None
        self.on_message_complete: Optional[Callable[[Message], None]] = None
        self.on_tool_call: Optional[Callable[[str, Dict[str, Any]], None]] = None
        self.on_tool_result: Optional[Callable[[str, ToolResult], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
    
    async def start_conversation(
        self,
        system_message: Optional[str] = None,
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        session_id: Optional[str] = None,
    ) -> ConversationContext:
        """Start a new conversation or resume an existing one.
        
        Args:
            system_message: System message to set context
            model: Model to use for this conversation
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            session_id: Existing session ID to resume
            
        Returns:
            ConversationContext for the session
        """
        if session_id and self.session_manager:
            # Try to resume existing session
            try:
                self.context = await self.session_manager.load_session(session_id)
                logger.info(f"Resumed conversation session: {session_id}")
            except Exception as e:
                logger.warning(f"Failed to resume session {session_id}: {e}")
                self.context = None
        
        if not self.context:
            # Generate enhanced system prompt
            try:
                enhanced_system_message = self.system_prompt_generator.generate_system_prompt(
                    user_system_message=system_message,
                    conversation_context=[]  # Empty for new conversations
                )
                logger.debug(f"Generated enhanced system prompt ({len(enhanced_system_message)} chars)")
            except Exception as e:
                logger.warning(f"Failed to generate enhanced system prompt: {e}")
                enhanced_system_message = system_message or "You are a helpful AI assistant."

            # Create new conversation
            self.context = ConversationContext(
                session_id=session_id or str(uuid.uuid4()),
                provider=self.provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                system_message=enhanced_system_message,
            )
            logger.info(f"Started new conversation session: {self.context.session_id}")

        return self.context

    async def update_system_prompt(self, user_system_message: Optional[str] = None) -> None:
        """Update the system prompt for the current conversation.

        Args:
            user_system_message: Optional user-provided system message to include
        """
        if not self.context:
            raise ValueError("No active conversation. Call start_conversation() first.")

        try:
            # Use current conversation context for adaptive prompts
            conversation_context = self.context.messages if self.context else []

            enhanced_system_message = self.system_prompt_generator.generate_system_prompt(
                user_system_message=user_system_message,
                conversation_context=conversation_context
            )

            # Update the context's system message
            self.context.system_message = enhanced_system_message
            self.context.updated_at = datetime.now()

            # Save session if manager is available
            if self.session_manager:
                await self.session_manager.save_session(self.context)

            logger.info(f"Updated system prompt for session: {self.context.session_id}")

        except Exception as e:
            logger.error(f"Failed to update system prompt: {e}")
            raise

    async def regenerate_system_prompt(self) -> str:
        """Regenerate and return the current system prompt without updating the conversation.

        Returns:
            The generated system prompt string
        """
        try:
            current_user_message = None
            if self.context and self.context.system_message:
                # Try to extract user portion from existing system message
                # This is a simple heuristic - in practice, you might want to store this separately
                if "## Additional Instructions" in self.context.system_message:
                    parts = self.context.system_message.split("## Additional Instructions")
                    if len(parts) > 1:
                        current_user_message = parts[1].strip()

            return self.system_prompt_generator.generate_system_prompt(
                user_system_message=current_user_message
            )
        except Exception as e:
            logger.error(f"Failed to regenerate system prompt: {e}")
            return "You are a helpful AI assistant."

    async def send_message(
        self,
        content: str,
        role: MessageRole = MessageRole.USER,
        stream: bool = True,
        files: Optional[List[str]] = None,
    ) -> AsyncIterator[StreamChunk]:
        """Send a message and get AI response.
        
        Args:
            content: Message content
            role: Message role
            stream: Whether to stream the response
            files: Optional list of file paths to include
            
        Yields:
            StreamChunk objects with response content
        """
        if not self.context:
            raise ValueError("No active conversation. Call start_conversation() first.")
        
        # Process file attachments
        if files:
            content = await self._process_file_attachments(content, files)
        
        # Add user message to context
        user_message = Message(role=role, content=content)
        self.context.add_message(user_message)
        
        if self.on_message_start:
            self.on_message_start(user_message)
        
        # Save session if manager is available
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        # Get AI response
        try:
            if stream:
                async for chunk in self._stream_response():
                    yield chunk
            else:
                response = await self._complete_response()
                yield StreamChunk(
                    content=response.content,
                    is_complete=True,
                    metadata=response.metadata,
                )
        except Exception as e:
            logger.error(f"Failed to get AI response: {e}", exc_info=True)
            if self.on_error:
                self.on_error(e)
            raise
    
    async def _stream_response(self) -> AsyncIterator[StreamChunk]:
        """Stream AI response with tool calling support and intelligent caching."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None

        # Optimize context for provider capabilities
        optimized_messages = self.context_manager.optimize_context_for_provider(
            messages, self.provider.capabilities
        )

        # Check cache for similar responses (only for non-tool requests)
        cached_response = None
        if not tools and self.context.temperature < 0.3:  # Only cache deterministic responses
            cached_response = self.response_cache.get_cached_response(
                optimized_messages, self.context.model or "default", self.context.temperature
            )

        if cached_response:
            logger.debug("Using cached response")
            yield StreamChunk(
                content=cached_response,
                is_complete=True,
                metadata={"cached": True, "model": self.context.model}
            )
            return

        with log_performance(logger, "stream_completion", model=self.context.model):
            accumulated_content = ""
            accumulated_tool_calls = []
            
            async for chunk in self.provider.stream_complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            ):
                # Handle content chunks
                if chunk.content:
                    accumulated_content += chunk.content
                    if self.on_message_chunk:
                        self.on_message_chunk(chunk.content)
                    yield chunk
                
                # Handle tool calls
                if chunk.tool_calls:
                    accumulated_tool_calls.extend(chunk.tool_calls)
                
                # Handle completion
                if chunk.is_complete:
                    # Create assistant message
                    assistant_message = Message(
                        role=MessageRole.ASSISTANT,
                        content=accumulated_content,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                    )
                    self.context.add_message(assistant_message)
                    
                    if self.on_message_complete:
                        self.on_message_complete(assistant_message)
                    
                    # Execute tool calls if present
                    if accumulated_tool_calls:
                        async for tool_chunk in self._execute_tool_calls(accumulated_tool_calls):
                            yield tool_chunk
                    
                    # Cache response if appropriate
                    if not accumulated_tool_calls and self.context.temperature < 0.3:
                        self.response_cache.cache_response(
                            optimized_messages,
                            self.context.model or "default",
                            self.context.temperature,
                            accumulated_content,
                            {"finish_reason": chunk.metadata.get("finish_reason")}
                        )

                    # Save session
                    if self.session_manager:
                        await self.session_manager.save_session(self.context)

                    yield chunk
                    break
    
    async def _complete_response(self) -> CompletionResponse:
        """Get complete AI response (non-streaming)."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None
        
        with log_performance(logger, "completion", model=self.context.model):
            response = await self.provider.complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            )
        
        # Add assistant message to context
        assistant_message = Message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            tool_calls=response.tool_calls,
        )
        self.context.add_message(assistant_message)
        
        if self.on_message_complete:
            self.on_message_complete(assistant_message)
        
        # Execute tool calls if present
        if response.tool_calls:
            await self._execute_tool_calls_sync(response.tool_calls)
        
        # Save session
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        return response
    
    async def _execute_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> AsyncIterator[StreamChunk]:
        """Execute tool calls and yield results."""
        for tool_call in tool_calls:
            tool_name = tool_call["function"]["name"]
            tool_args = json.loads(tool_call["function"]["arguments"])
            tool_id = tool_call.get("id", str(uuid.uuid4()))

            if self.on_tool_call:
                self.on_tool_call(tool_name, tool_args)

            # Check approval if required
            if self.approval_manager:
                approved = await self.approval_manager.request_approval(
                    tool_name, tool_args, self.context
                )
                if not approved:
                    result = ToolResult(
                        success=False,
                        content="Tool execution denied by approval policy",
                        metadata={"tool": tool_name, "reason": "approval_denied"}
                    )

                    # Add tool result message with proper tool_call_id
                    tool_message = Message(
                        role=MessageRole.TOOL,
                        content=result.content,
                        name=tool_name,
                        tool_call_id=tool_id,
                    )
                    self.context.add_message(tool_message)

                    yield StreamChunk(
                        content=f"\n[Tool execution denied: {tool_name}]\n",
                        is_complete=False,
                        metadata={"tool_result": result.dict()}
                    )
                    continue

            # Execute tool
            try:
                result = await self.tool_registry.execute_tool(tool_name, tool_args)

                if self.on_tool_result:
                    self.on_tool_result(tool_name, result)

                # Add tool result message with proper tool_call_id
                tool_message = Message(
                    role=MessageRole.TOOL,
                    content=result.content,
                    name=tool_name,
                    tool_call_id=tool_id,
                )
                self.context.add_message(tool_message)

                # Yield tool result
                yield StreamChunk(
                    content=f"\n[Tool: {tool_name}]\n{result.content}\n",
                    is_complete=False,
                    metadata={"tool_result": result.dict()}
                )

            except Exception as e:
                logger.error(f"Tool execution failed: {tool_name}: {e}", exc_info=True)
                error_result = ToolResult(
                    success=False,
                    content=f"Tool execution failed: {e}",
                    metadata={"tool": tool_name, "error": str(e)}
                )

                # Add error result message with proper tool_call_id
                tool_message = Message(
                    role=MessageRole.TOOL,
                    content=error_result.content,
                    name=tool_name,
                    tool_call_id=tool_id,
                )
                self.context.add_message(tool_message)

                yield StreamChunk(
                    content=f"\n[Tool error: {tool_name}] {e}\n",
                    is_complete=False,
                    metadata={"tool_result": error_result.dict()}
                )
    
    async def _execute_tool_calls_sync(self, tool_calls: List[Dict[str, Any]]) -> None:
        """Execute tool calls synchronously (for non-streaming mode)."""
        async for _ in self._execute_tool_calls(tool_calls):
            pass  # Just execute, don't yield
    
    async def _process_file_attachments(self, content: str, files: List[str]) -> str:
        """Process file attachments and include them in the message."""
        # This would integrate with the file operations utility
        # For now, just mention the files
        file_list = ", ".join(files)
        return f"{content}\n\nAttached files: {file_list}"
    
    async def clear_conversation(self) -> None:
        """Clear the current conversation."""
        if self.context:
            self.context.messages.clear()
            self.context.updated_at = datetime.now()
            
            if self.session_manager:
                await self.session_manager.save_session(self.context)
    
    async def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.context or not self.context.messages:
            return "No conversation history."
        
        message_count = len(self.context.messages)
        user_messages = len([m for m in self.context.messages if m.role == MessageRole.USER])
        assistant_messages = len([m for m in self.context.messages if m.role == MessageRole.ASSISTANT])
        
        return (
            f"Session: {self.context.session_id}\n"
            f"Messages: {message_count} total ({user_messages} user, {assistant_messages} assistant)\n"
            f"Model: {self.context.model or 'default'}\n"
            f"Started: {self.context.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Updated: {self.context.updated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )

    async def get_conversation_insights(self) -> Dict[str, Any]:
        """Get insights about the current conversation."""
        if not self.context or not self.context.messages:
            return {"error": "No conversation history"}

        patterns = self.context_manager.analyze_conversation_patterns(self.context.messages)
        insights = self.context_manager.extract_key_insights(self.context.messages)

        return {
            "session_id": self.context.session_id,
            "message_count": len(self.context.messages),
            "patterns": patterns,
            "insights": insights,
            "cache_stats": {
                "cache_size": len(self.response_cache.cache),
                "cache_hits": len([k for k, v in self.response_cache.access_times.items()
                                if (datetime.now() - v).total_seconds() < 3600])  # Hits in last hour
            }
        }

    async def optimize_conversation_context(self) -> None:
        """Optimize the conversation context for better performance."""
        if not self.context:
            return

        # Optimize messages for provider capabilities
        optimized_messages = self.context_manager.optimize_context_for_provider(
            self.context.messages, self.provider.capabilities
        )

        if len(optimized_messages) < len(self.context.messages):
            logger.info(f"Optimized context: {len(self.context.messages)} -> {len(optimized_messages)} messages")
            self.context.messages = optimized_messages
            self.context.updated_at = datetime.now()

            # Save optimized session
            if self.session_manager:
                await self.session_manager.save_session(self.context)

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get response cache statistics."""
        return {
            "cache_size": len(self.response_cache.cache),
            "max_cache_size": self.response_cache.max_cache_size,
            "cache_ttl_hours": self.response_cache.cache_ttl.total_seconds() / 3600,
            "recent_access_count": len([
                k for k, v in self.response_cache.access_times.items()
                if (datetime.now() - v).total_seconds() < 3600
            ])
        }

    def clear_cache(self) -> None:
        """Clear the response cache."""
        self.response_cache.cache.clear()
        self.response_cache.access_times.clear()
        logger.info("Response cache cleared")

    async def analyze_performance_metrics(self) -> Dict[str, Any]:
        """Analyze performance metrics for the current session."""
        if not self.context:
            return {"error": "No active conversation"}

        metrics = {
            "session_duration": (datetime.now() - self.context.created_at).total_seconds(),
            "message_count": len(self.context.messages),
            "average_response_time": 0,  # Would need to track this
            "tool_usage_count": len([m for m in self.context.messages if m.tool_calls]),
            "cache_efficiency": 0,  # Would calculate based on cache hits/misses
            "context_optimization_ratio": 1.0,  # Would calculate based on context trimming
        }
        return metrics

