"""
Interactive setup wizard for AI Terminal.

Guides new users through initial configuration including provider selection,
API key setup, model preferences, and security settings.
"""

import asyncio
import os
from typing import Dict, Any, Optional, List
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.progress import Progress, SpinnerColumn, TextColumn

from ai_terminal.config.settings import get_settings, Settings
from ai_terminal.config.providers import get_all_providers, ProviderType, ProviderConfig
from ai_terminal.ui.overlays import ProviderSelectionOverlay, OverlayResult
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class SetupWizard:
    """Interactive setup wizard for first-time configuration."""
    
    def __init__(self):
        """Initialize setup wizard."""
        self.console = Console()
        self.settings = get_settings()
        self.config_data = {}
    
    async def run(self) -> bool:
        """Run the complete setup wizard.
        
        Returns:
            True if setup completed successfully
        """
        try:
            self.console.clear()
            self._show_welcome()
            
            # Step 1: Provider selection and API key setup
            if not await self._setup_providers():
                return False
            
            # Step 2: Model preferences
            if not await self._setup_model_preferences():
                return False
            
            # Step 3: Security settings
            if not await self._setup_security():
                return False
            
            # Step 4: UI preferences
            if not await self._setup_ui_preferences():
                return False
            
            # Step 5: Save configuration
            if not await self._save_configuration():
                return False
            
            self._show_completion()
            return True
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Setup cancelled by user[/yellow]")
            return False
        except Exception as e:
            logger.error(f"Setup wizard error: {e}", exc_info=True)
            self.console.print(f"[red]Setup failed: {e}[/red]")
            return False
    
    def _show_welcome(self):
        """Show welcome message."""
        welcome_text = Text()
        welcome_text.append("🚀 Welcome to AI Terminal Setup!\n\n", style="bold blue")
        welcome_text.append("This wizard will help you configure AI Terminal for first use.\n", style="white")
        welcome_text.append("You'll set up:\n", style="white")
        welcome_text.append("• AI providers and API keys\n", style="dim")
        welcome_text.append("• Model preferences\n", style="dim")
        welcome_text.append("• Security settings\n", style="dim")
        welcome_text.append("• Interface preferences\n", style="dim")
        welcome_text.append("\nPress Enter to continue...", style="dim")
        
        panel = Panel(
            welcome_text,
            title="AI Terminal Setup",
            border_style="blue",
            padding=(1, 2),
        )
        self.console.print(panel)
        input()  # Wait for user
    
    async def _setup_providers(self) -> bool:
        """Setup AI providers and API keys."""
        self.console.clear()
        self._show_step_header("Step 1: AI Provider Setup", "Configure your AI providers")
        
        # Show available providers
        providers = get_all_providers()
        
        self.console.print("[bold]Available AI Providers:[/bold]\n")
        
        provider_table = Table(show_header=True, header_style="bold magenta")
        provider_table.add_column("Provider", style="cyan")
        provider_table.add_column("Description", style="white")
        provider_table.add_column("API Key Required", style="yellow")
        provider_table.add_column("Status", style="green")

        for provider_type, config in providers.items():
            api_key_required = "Yes" if config.api_key_env else "No"

            # Check if already configured
            if config.api_key_env:
                status = "✅ Configured" if os.getenv(config.api_key_env) else "❌ Not configured"
            else:
                status = "✅ Ready (Local)"

            # Create description from provider info
            description = self._get_provider_description(config)

            provider_table.add_row(
                config.display_name,
                description,
                api_key_required,
                status
            )
        
        self.console.print(provider_table)
        self.console.print()
        
        # Let user select and configure providers
        while True:
            setup_provider = Confirm.ask("Would you like to configure a provider?", default=True)
            
            if not setup_provider:
                break
            
            # Use provider selection overlay
            provider_overlay = ProviderSelectionOverlay(self.console)
            result, selected_provider = await provider_overlay.show()
            
            if result == OverlayResult.CONFIRMED and selected_provider:
                self.config_data["default_provider"] = selected_provider
                self.console.print(f"[green]Set {selected_provider} as default provider[/green]")
                break
            elif result == OverlayResult.CANCELLED:
                continue
            else:
                self.console.print("[red]Provider setup failed[/red]")
                return False
        
        # Check if at least one provider is configured
        configured_providers = []
        for provider_type, config in providers.items():
            if not config.api_key_env or os.getenv(config.api_key_env):
                configured_providers.append(provider_type.value)
        
        if not configured_providers:
            self.console.print("[red]No providers configured. At least one provider is required.[/red]")
            return False
        
        self.config_data["available_providers"] = configured_providers
        return True
    
    async def _setup_model_preferences(self) -> bool:
        """Setup model preferences."""
        self.console.clear()
        self._show_step_header("Step 2: Model Preferences", "Configure default model settings")
        
        # Temperature setting
        self.console.print("[bold]Temperature Setting[/bold]")
        self.console.print("Controls randomness in AI responses:")
        self.console.print("• 0.0 = Very focused and deterministic")
        self.console.print("• 0.7 = Balanced (recommended)")
        self.console.print("• 1.0 = More creative and varied\n")
        
        temperature = float(Prompt.ask(
            "Default temperature",
            default="0.7",
            show_default=True
        ))
        
        # Max tokens setting
        self.console.print("\n[bold]Max Tokens Setting[/bold]")
        self.console.print("Maximum length of AI responses:")
        self.console.print("• 1000 = Short responses")
        self.console.print("• 2000 = Medium responses (recommended)")
        self.console.print("• 4000 = Long responses\n")
        
        max_tokens = int(Prompt.ask(
            "Default max tokens",
            default="2000",
            show_default=True
        ))
        
        self.config_data.update({
            "default_temperature": temperature,
            "default_max_tokens": max_tokens,
        })
        
        return True
    
    async def _setup_security(self) -> bool:
        """Setup security preferences."""
        self.console.clear()
        self._show_step_header("Step 3: Security Settings", "Configure command execution safety")
        
        self.console.print("[bold]Command Execution Security[/bold]\n")
        self.console.print("AI Terminal can execute system commands. Configure safety settings:\n")
        
        # Sandbox enabled
        sandbox_enabled = Confirm.ask(
            "Enable command sandboxing? (Recommended)",
            default=True
        )
        
        # Auto-approve safe commands
        auto_approve_safe = Confirm.ask(
            "Auto-approve safe commands? (e.g., ls, cat, pwd)",
            default=True
        )
        
        # Require approval for file operations
        require_file_approval = Confirm.ask(
            "Require approval for file operations? (Recommended)",
            default=True
        )
        
        self.config_data.update({
            "sandbox_enabled": sandbox_enabled,
            "auto_approve_safe": auto_approve_safe,
            "require_file_approval": require_file_approval,
        })
        
        return True
    
    async def _setup_ui_preferences(self) -> bool:
        """Setup UI preferences."""
        self.console.clear()
        self._show_step_header("Step 4: Interface Preferences", "Customize your AI Terminal experience")
        
        # Streaming responses
        streaming = Confirm.ask(
            "Enable streaming responses? (Shows AI typing in real-time)",
            default=True
        )
        
        # Syntax highlighting
        syntax_highlighting = Confirm.ask(
            "Enable syntax highlighting for code?",
            default=True
        )
        
        # Compact mode
        compact_mode = Confirm.ask(
            "Use compact display mode? (Less visual padding)",
            default=False
        )
        
        # Theme selection
        self.console.print("\n[bold]Theme Selection[/bold]")
        theme = Prompt.ask(
            "Choose theme",
            choices=["dark", "light"],
            default="dark"
        )
        
        self.config_data.update({
            "streaming": streaming,
            "syntax_highlighting": syntax_highlighting,
            "compact_mode": compact_mode,
            "theme": theme,
        })
        
        return True
    
    async def _save_configuration(self) -> bool:
        """Save configuration to files."""
        self.console.clear()
        self._show_step_header("Step 5: Saving Configuration", "Writing settings to disk")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("Saving configuration...", total=None)
            
            try:
                # Create config directory if it doesn't exist
                config_dir = self.settings.config_dir
                config_dir.mkdir(parents=True, exist_ok=True)
                
                progress.update(task, description="Creating configuration files...")

                # Update settings with configuration data
                if "default_provider" in self.config_data:
                    self.settings.default_provider = self.config_data["default_provider"]

                if "default_temperature" in self.config_data:
                    # Note: Settings doesn't have temperature field, but we can add it to the config file
                    pass

                if "default_max_tokens" in self.config_data:
                    # Note: Settings doesn't have max_tokens field, but we can add it to the config file
                    pass

                # Save the updated settings to file
                config_file = self.settings.config_dir / "config.yaml"

                # Create a comprehensive config dictionary
                config_dict = {
                    "default_provider": self.config_data.get("default_provider", "openai"),
                    "default_model": "deepseek-chat" if self.config_data.get("default_provider") == "deepseek" else "gpt-4",
                    "default_temperature": self.config_data.get("default_temperature", 0.7),
                    "default_max_tokens": self.config_data.get("default_max_tokens", 2000),
                    "streaming": self.config_data.get("streaming", True),
                    "syntax_highlighting": self.config_data.get("syntax_highlighting", True),
                    "compact_mode": self.config_data.get("compact_mode", False),
                    "theme": self.config_data.get("theme", "dark"),
                    "security": {
                        "sandbox_enabled": self.config_data.get("sandbox_enabled", True),
                        "auto_approve_safe": self.config_data.get("auto_approve_safe", True),
                        "require_file_approval": self.config_data.get("require_file_approval", True),
                    }
                }

                # Save to YAML file
                import yaml
                with open(config_file, "w") as f:
                    yaml.dump(config_dict, f, default_flow_style=False, indent=2)

                progress.update(task, description="Configuration saved!")

                self.console.print("\n[green]Configuration saved successfully![/green]")
                self.console.print("\n[bold]Your settings:[/bold]")

                for key, value in self.config_data.items():
                    self.console.print(f"  {key}: {value}")

                return True
                
            except Exception as e:
                logger.error(f"Failed to save configuration: {e}")
                self.console.print(f"[red]Failed to save configuration: {e}[/red]")
                return False
    
    def _show_step_header(self, title: str, subtitle: str):
        """Show step header."""
        header_text = Text()
        header_text.append(title, style="bold blue")
        header_text.append(f"\n{subtitle}", style="dim")
        
        panel = Panel(
            header_text,
            border_style="blue",
            padding=(1, 2),
        )
        self.console.print(panel)
        self.console.print()
    
    def _show_completion(self):
        """Show completion message."""
        completion_text = Text()
        completion_text.append("🎉 Setup Complete!\n\n", style="bold green")
        completion_text.append("AI Terminal is now configured and ready to use.\n\n", style="white")

        # Show API key setup instructions if needed
        if "default_provider" in self.config_data and self.config_data["default_provider"] != "ollama":
            provider_name = self.config_data["default_provider"].upper()
            completion_text.append("⚠️  Important: API Key Setup\n", style="bold yellow")
            completion_text.append(f"To use {provider_name}, set your API key permanently:\n\n", style="white")
            completion_text.append("Windows (PowerShell):\n", style="bold")
            completion_text.append(f"[System.Environment]::SetEnvironmentVariable('{provider_name}_API_KEY', 'your-api-key', 'User')\n\n", style="cyan")
            completion_text.append("Windows (Command Prompt):\n", style="bold")
            completion_text.append(f"setx {provider_name}_API_KEY your-api-key\n\n", style="cyan")
            completion_text.append("Linux/macOS:\n", style="bold")
            completion_text.append(f"echo 'export {provider_name}_API_KEY=your-api-key' >> ~/.bashrc\n", style="cyan")
            completion_text.append("source ~/.bashrc\n\n", style="cyan")

        completion_text.append("Quick start commands:\n", style="bold")
        completion_text.append("• python cli.py chat          - Start interactive chat\n", style="cyan")
        completion_text.append("• python cli.py quick 'help'  - Send a quick message\n", style="cyan")
        completion_text.append("• python cli.py --help        - Show all commands\n", style="cyan")
        completion_text.append("\nEnjoy using AI Terminal! 🚀", style="dim")
        
        panel = Panel(
            completion_text,
            title="Setup Complete",
            border_style="green",
            padding=(1, 2),
        )
        self.console.print(panel)

    def _get_provider_description(self, config: ProviderConfig) -> str:
        """Get a description for the provider based on its configuration.

        Args:
            config: Provider configuration

        Returns:
            Description string
        """
        descriptions = {
            "openai": "Advanced AI models including GPT-4 and GPT-3.5",
            "deepseek": "High-performance coding models optimized for development",
            "anthropic": "Claude family models with strong reasoning capabilities",
            "ollama": "Local AI models running on your machine"
        }

        return descriptions.get(config.name, f"AI provider with {len(config.models)} models")


async def run_setup_wizard() -> bool:
    """Run the setup wizard.
    
    Returns:
        True if setup completed successfully
    """
    wizard = SetupWizard()
    return await wizard.run()


if __name__ == "__main__":
    # Allow running the wizard directly
    asyncio.run(run_setup_wizard())
