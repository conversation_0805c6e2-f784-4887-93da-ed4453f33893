"""
AI Terminal - Sophisticated AI-powered CLI terminal tool.

A modern, enterprise-grade AI assistant that brings advanced AI capabilities
directly to your command line with security, performance, and developer experience
as core principles.
"""

__version__ = "0.1.0"
__author__ = "AI Terminal Team"
__email__ = "<EMAIL>"

from ai_terminal.config.settings import Settings
from ai_terminal.providers.base import BaseProvider
from ai_terminal.agents.agent_loop import Agent<PERSON>oop

__all__ = [
    "Settings",
    "BaseProvider", 
    "AgentLoop",
    "__version__",
]
