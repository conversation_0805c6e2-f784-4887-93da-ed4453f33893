"""
Modern terminal chat interface using Rich for AI Terminal.

Provides a sophisticated chat experience with streaming responses,
syntax highlighting, and interactive features.
"""

import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
import re

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.prompt import Prompt
from rich.live import Live
from rich.layout import Layout
from rich.align import Align

from ai_terminal.agents.agent_loop import Agent<PERSON>oop, ConversationContext
from ai_terminal.providers.base import Message, MessageRole
from ai_terminal.config.settings import Settings
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class TerminalChat:
    """Modern terminal chat interface."""
    
    def __init__(self, agent: AgentLoop, settings: Settings):
        """Initialize the chat interface.
        
        Args:
            agent: Agent loop for AI interactions
            settings: Application settings
        """
        self.agent = agent
        self.settings = settings
        self.console = Console()
        self.running = False
        
        # Chat state
        self.conversation_history: List[Dict[str, Any]] = []
        self.current_response = ""
        
        # UI settings
        self.compact_mode = settings.ui.compact_mode
        self.syntax_highlighting = settings.ui.syntax_highlighting
        
        # Setup event callbacks
        self._setup_agent_callbacks()
    
    def _setup_agent_callbacks(self):
        """Setup callbacks for agent events."""
        self.agent.on_message_start = self._on_message_start
        self.agent.on_message_chunk = self._on_message_chunk
        self.agent.on_message_complete = self._on_message_complete
        self.agent.on_tool_call = self._on_tool_call
        self.agent.on_tool_result = self._on_tool_result
        self.agent.on_error = self._on_error
    
    async def run(
        self,
        model: Optional[str] = None,
        temperature: float = 0.7,
        system_message: Optional[str] = None,
        session_id: Optional[str] = None,
        stream: bool = True,
    ):
        """Run the chat interface.
        
        Args:
            model: Model to use
            temperature: Sampling temperature
            system_message: System message
            session_id: Session ID to resume
            stream: Enable streaming responses
        """
        self.running = True
        
        # Show welcome message
        self._show_welcome()
        
        # Start conversation
        context = await self.agent.start_conversation(
            model=model,
            temperature=temperature,
            system_message=system_message,
            session_id=session_id,
        )
        
        # Show session info
        self._show_session_info(context)
        
        # Main chat loop
        try:
            while self.running:
                # Get user input
                user_input = await self._get_user_input()
                
                if not user_input:
                    continue
                
                # Handle slash commands
                if user_input.startswith('/'):
                    await self._handle_slash_command(user_input)
                    continue
                
                # Process file tags (@file.txt)
                user_input, files = self._process_file_tags(user_input)
                
                # Send message to AI
                await self._send_message(user_input, files, stream)
                
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Chat session ended by user[/yellow]")
        except Exception as e:
            logger.error(f"Chat error: {e}", exc_info=True)
            self.console.print(f"[red]Error: {e}[/red]")
        finally:
            self.running = False
    
    def _show_welcome(self):
        """Show welcome message."""
        welcome_text = Text()
        welcome_text.append("AI Terminal", style="bold blue")
        welcome_text.append(" - Sophisticated AI-powered CLI tool\n\n", style="blue")
        welcome_text.append("Commands:\n", style="bold")
        welcome_text.append("  /help     - Show help\n", style="dim")
        welcome_text.append("  /model    - Switch model\n", style="dim")
        welcome_text.append("  /clear    - Clear conversation\n", style="dim")
        welcome_text.append("  /history  - Show history\n", style="dim")
        welcome_text.append("  /exit     - Exit chat\n", style="dim")
        welcome_text.append("\nUse @filename to include files in your message\n", style="dim")
        
        panel = Panel(
            welcome_text,
            title="Welcome",
            border_style="blue",
            padding=(1, 2),
        )
        self.console.print(panel)
    
    def _show_session_info(self, context: ConversationContext):
        """Show session information."""
        info_text = Text()
        info_text.append(f"Session: {context.session_id[:8]}...\n", style="dim")
        info_text.append(f"Provider: {context.provider.name}\n", style="dim")
        info_text.append(f"Model: {context.model or 'default'}\n", style="dim")
        info_text.append(f"Temperature: {context.temperature}", style="dim")
        
        self.console.print(Panel(info_text, title="Session Info", border_style="green"))
    
    async def _get_user_input(self) -> str:
        """Get user input with prompt."""
        try:
            # Use Rich prompt for better UX
            prompt_text = Text()
            prompt_text.append("You", style="bold blue")
            prompt_text.append("> ", style="blue")
            
            self.console.print(prompt_text, end="")
            
            # Get input (this is synchronous, but we're in an async context)
            user_input = input().strip()
            return user_input
            
        except EOFError:
            return "/exit"
        except KeyboardInterrupt:
            raise
    
    async def _send_message(self, content: str, files: List[str], stream: bool):
        """Send message to AI and display response."""
        # Show user message
        self._display_user_message(content, files)
        
        # Prepare for AI response
        self.current_response = ""
        
        if stream:
            # Stream response with live updates
            with Live(self._create_response_panel(""), refresh_per_second=10) as live:
                async for chunk in self.agent.send_message(content, files=files, stream=True):
                    if chunk.content:
                        self.current_response += chunk.content
                        live.update(self._create_response_panel(self.current_response))
                    
                    if chunk.is_complete:
                        break
        else:
            # Non-streaming response
            async for chunk in self.agent.send_message(content, files=files, stream=False):
                if chunk.content:
                    self.current_response = chunk.content
                    self.console.print(self._create_response_panel(self.current_response))
                
                if chunk.is_complete:
                    break
        
        # Add to conversation history
        self.conversation_history.append({
            "role": "user",
            "content": content,
            "files": files,
            "timestamp": datetime.now(),
        })
        self.conversation_history.append({
            "role": "assistant",
            "content": self.current_response,
            "timestamp": datetime.now(),
        })
    
    def _display_user_message(self, content: str, files: List[str]):
        """Display user message."""
        message_text = Text()
        message_text.append("You", style="bold blue")
        message_text.append(f": {content}", style="white")
        
        if files:
            message_text.append(f"\nFiles: {', '.join(files)}", style="dim")
        
        if not self.compact_mode:
            panel = Panel(message_text, border_style="blue", padding=(0, 1))
            self.console.print(panel)
        else:
            self.console.print(message_text)
    
    def _create_response_panel(self, content: str) -> Panel:
        """Create a panel for AI response."""
        if not content:
            content = "Thinking..."
        
        # Apply syntax highlighting if enabled
        if self.syntax_highlighting and self._contains_code(content):
            rendered_content = self._render_with_syntax_highlighting(content)
        else:
            rendered_content = content
        
        title_text = Text()
        title_text.append("AI", style="bold green")
        
        if self.compact_mode:
            return Panel(rendered_content, title=title_text, border_style="green", padding=(0, 1))
        else:
            return Panel(rendered_content, title=title_text, border_style="green", padding=(1, 2))
    
    def _contains_code(self, content: str) -> bool:
        """Check if content contains code blocks."""
        return "```" in content or "`" in content
    
    def _render_with_syntax_highlighting(self, content: str):
        """Render content with syntax highlighting."""
        # Simple markdown-like rendering
        lines = content.split('\n')
        rendered_lines = []
        in_code_block = False
        code_language = None
        code_lines = []
        
        for line in lines:
            if line.startswith('```'):
                if in_code_block:
                    # End of code block
                    if code_lines and code_language:
                        try:
                            syntax = Syntax('\n'.join(code_lines), code_language, theme="monokai")
                            rendered_lines.append(syntax)
                        except Exception:
                            rendered_lines.extend(code_lines)
                    else:
                        rendered_lines.extend(code_lines)
                    code_lines = []
                    in_code_block = False
                    code_language = None
                else:
                    # Start of code block
                    in_code_block = True
                    code_language = line[3:].strip() or "text"
            elif in_code_block:
                code_lines.append(line)
            else:
                # Regular text with inline code highlighting
                if '`' in line:
                    # Simple inline code highlighting
                    parts = line.split('`')
                    text = Text()
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            text.append(part)
                        else:
                            text.append(part, style="bold cyan")
                    rendered_lines.append(text)
                else:
                    rendered_lines.append(line)
        
        # Handle unclosed code block
        if in_code_block and code_lines:
            if code_language:
                try:
                    syntax = Syntax('\n'.join(code_lines), code_language, theme="monokai")
                    rendered_lines.append(syntax)
                except Exception:
                    rendered_lines.extend(code_lines)
            else:
                rendered_lines.extend(code_lines)
        
        return '\n'.join(str(line) for line in rendered_lines)
    
    def _process_file_tags(self, content: str) -> tuple[str, List[str]]:
        """Process @file.txt tags in content."""
        files = []
        
        # Find all @filename patterns
        file_pattern = r'@([^\s]+)'
        matches = re.findall(file_pattern, content)
        
        for match in matches:
            files.append(match)
            # Remove the tag from content
            content = content.replace(f'@{match}', f'[File: {match}]')
        
        return content, files
    
    async def _handle_slash_command(self, command: str):
        """Handle slash commands."""
        parts = command[1:].split()
        cmd = parts[0].lower() if parts else ""
        
        if cmd == "help":
            self._show_help()
        elif cmd == "exit" or cmd == "quit":
            self.running = False
        elif cmd == "clear":
            await self._clear_conversation()
        elif cmd == "history":
            self._show_history()
        elif cmd == "model":
            await self._switch_model()
        elif cmd == "compact":
            self._toggle_compact_mode()
        elif cmd == "syntax":
            self._toggle_syntax_highlighting()
        else:
            self.console.print(f"[red]Unknown command: {cmd}[/red]")
            self.console.print("Type /help for available commands")
    
    def _show_help(self):
        """Show help information."""
        help_text = Text()
        help_text.append("Available Commands:\n\n", style="bold")
        help_text.append("/help      - Show this help\n", style="cyan")
        help_text.append("/exit      - Exit chat\n", style="cyan")
        help_text.append("/clear     - Clear conversation\n", style="cyan")
        help_text.append("/history   - Show conversation history\n", style="cyan")
        help_text.append("/model     - Switch AI model\n", style="cyan")
        help_text.append("/compact   - Toggle compact mode\n", style="cyan")
        help_text.append("/syntax    - Toggle syntax highlighting\n", style="cyan")
        help_text.append("\nFile Inclusion:\n", style="bold")
        help_text.append("@filename  - Include file in message\n", style="cyan")
        help_text.append("Example: 'Review this code @script.py'\n", style="dim")
        
        panel = Panel(help_text, title="Help", border_style="yellow")
        self.console.print(panel)
    
    async def _clear_conversation(self):
        """Clear conversation history."""
        await self.agent.clear_conversation()
        self.conversation_history.clear()
        self.console.print("[green]Conversation cleared[/green]")
    
    def _show_history(self):
        """Show conversation history."""
        if not self.conversation_history:
            self.console.print("[yellow]No conversation history[/yellow]")
            return
        
        self.console.print("[bold]Conversation History:[/bold]\n")
        
        for i, entry in enumerate(self.conversation_history[-10:], 1):  # Show last 10
            role = entry["role"]
            content = entry["content"][:100] + "..." if len(entry["content"]) > 100 else entry["content"]
            timestamp = entry["timestamp"].strftime("%H:%M:%S")
            
            style = "blue" if role == "user" else "green"
            self.console.print(f"[{style}]{i}. {role.title()} ({timestamp}):[/{style}] {content}")
    
    async def _switch_model(self):
        """Switch AI model."""
        from ai_terminal.ui.overlays import ModelSelectionOverlay, ProviderSelectionOverlay, OverlayResult

        # First, let user choose provider or model
        choice = input("Switch (p)rovider or (m)odel? [m]: ").lower().strip()

        if choice == 'p' or choice == 'provider':
            # Switch provider
            provider_overlay = ProviderSelectionOverlay(self.console)
            result, selected_provider = await provider_overlay.show(
                current_provider=self.agent.context.provider.name
            )

            if result == OverlayResult.CONFIRMED and selected_provider:
                # TODO: Implement provider switching in agent
                self.console.print(f"[green]Provider switching to {selected_provider} - feature coming soon![/green]")
        else:
            # Switch model
            model_overlay = ModelSelectionOverlay(self.console)
            result, selected_model = await model_overlay.show(
                provider=self.agent.context.provider,
                current_model=self.agent.context.model
            )

            if result == OverlayResult.CONFIRMED and selected_model:
                # Update the model in the agent context
                self.agent.context.model = selected_model
                self.console.print(f"[green]Switched to model: {selected_model}[/green]")
    
    def _toggle_compact_mode(self):
        """Toggle compact display mode."""
        self.compact_mode = not self.compact_mode
        status = "enabled" if self.compact_mode else "disabled"
        self.console.print(f"[green]Compact mode {status}[/green]")
    
    def _toggle_syntax_highlighting(self):
        """Toggle syntax highlighting."""
        self.syntax_highlighting = not self.syntax_highlighting
        status = "enabled" if self.syntax_highlighting else "disabled"
        self.console.print(f"[green]Syntax highlighting {status}[/green]")
    
    # Agent event callbacks
    def _on_message_start(self, message: Message):
        """Called when a message starts."""
        pass
    
    def _on_message_chunk(self, chunk: str):
        """Called when a message chunk is received."""
        pass
    
    def _on_message_complete(self, message: Message):
        """Called when a message is complete."""
        pass
    
    def _on_tool_call(self, tool_name: str, arguments: Dict[str, Any]):
        """Called when a tool is called."""
        self.console.print(f"[yellow]🔧 Calling tool: {tool_name}[/yellow]")
    
    def _on_tool_result(self, tool_name: str, result):
        """Called when a tool result is received."""
        status = "✅" if result.success else "❌"
        self.console.print(f"[yellow]{status} Tool {tool_name} completed[/yellow]")
    
    def _on_error(self, error: Exception):
        """Called when an error occurs."""
        self.console.print(f"[red]Error: {error}[/red]")
