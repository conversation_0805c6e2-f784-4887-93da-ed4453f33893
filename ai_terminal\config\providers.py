"""
AI provider configuration management.

Handles configuration for all supported AI providers including API endpoints,
model capabilities, and provider-specific settings.
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class ProviderType(str, Enum):
    """Supported AI provider types."""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    AZURE = "azure"
    GEMINI = "gemini"
    MISTRAL = "mistral"


class ModelCapabilities(BaseModel):
    """Model capability flags."""
    supports_functions: bool = Field(default=False, description="Supports function calling")
    supports_streaming: bool = Field(default=True, description="Supports streaming responses")
    supports_vision: bool = Field(default=False, description="Supports image input")
    supports_code: bool = Field(default=True, description="Optimized for code tasks")
    context_length: int = Field(default=4096, description="Maximum context length")
    max_tokens: int = Field(default=2048, description="Maximum output tokens")


class ModelInfo(BaseModel):
    """Information about a specific model."""
    name: str = Field(description="Model name/identifier")
    display_name: str = Field(description="Human-readable model name")
    description: str = Field(default="", description="Model description")
    capabilities: ModelCapabilities = Field(default_factory=ModelCapabilities)
    cost_per_1k_tokens: Optional[float] = Field(default=None, description="Cost per 1K tokens")


class ProviderConfig(BaseModel):
    """Configuration for an AI provider."""
    
    name: str = Field(description="Provider name")
    type: ProviderType = Field(description="Provider type")
    display_name: str = Field(description="Human-readable provider name")
    
    # API Configuration
    base_url: str = Field(description="Base API URL")
    api_key_env: str = Field(description="Environment variable for API key")
    api_version: Optional[str] = Field(default=None, description="API version")
    
    # Models
    models: List[ModelInfo] = Field(default_factory=list, description="Available models")
    default_model: str = Field(description="Default model name")
    
    # Provider-specific settings
    extra_headers: Dict[str, str] = Field(default_factory=dict, description="Extra HTTP headers")
    timeout: int = Field(default=60, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    
    # Rate limiting
    requests_per_minute: Optional[int] = Field(default=None, description="Rate limit")
    tokens_per_minute: Optional[int] = Field(default=None, description="Token rate limit")
    
    @validator("models", pre=True)
    def validate_models(cls, v, values):
        """Validate and convert model configurations."""
        if not v:
            return []
        
        models = []
        for model in v:
            if isinstance(model, str):
                # Convert string to ModelInfo
                models.append(ModelInfo(
                    name=model,
                    display_name=model.replace("-", " ").title(),
                ))
            elif isinstance(model, dict):
                models.append(ModelInfo(**model))
            else:
                models.append(model)
        
        return models


# Default provider configurations
DEFAULT_PROVIDERS = {
    ProviderType.OPENAI: ProviderConfig(
        name="openai",
        type=ProviderType.OPENAI,
        display_name="OpenAI",
        base_url="https://api.openai.com/v1",
        api_key_env="OPENAI_API_KEY",
        default_model="gpt-4",
        models=[
            ModelInfo(
                name="gpt-4",
                display_name="GPT-4",
                description="Most capable model, best for complex tasks",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_vision=False,
                    context_length=8192,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.03,
            ),
            ModelInfo(
                name="gpt-4-turbo-preview",
                display_name="GPT-4 Turbo",
                description="Latest GPT-4 with improved performance",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_vision=True,
                    context_length=128000,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.01,
            ),
            ModelInfo(
                name="gpt-3.5-turbo",
                display_name="GPT-3.5 Turbo",
                description="Fast and efficient for most tasks",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    context_length=16384,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.001,
            ),
        ],
        requests_per_minute=3000,
        tokens_per_minute=250000,
    ),
    
    ProviderType.DEEPSEEK: ProviderConfig(
        name="deepseek",
        type=ProviderType.DEEPSEEK,
        display_name="Deepseek",
        base_url="https://api.deepseek.com/v1",
        api_key_env="DEEPSEEK_API_KEY",
        default_model="deepseek-coder",
        models=[
            ModelInfo(
                name="deepseek-coder",
                display_name="Deepseek Coder",
                description="Specialized for coding tasks",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_code=True,
                    context_length=16384,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.0014,
            ),
            ModelInfo(
                name="deepseek-chat",
                display_name="Deepseek Chat",
                description="General purpose conversational model",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    context_length=32768,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.0014,
            ),
        ],
        requests_per_minute=1000,
        tokens_per_minute=100000,
    ),
    
    ProviderType.ANTHROPIC: ProviderConfig(
        name="anthropic",
        type=ProviderType.ANTHROPIC,
        display_name="Anthropic",
        base_url="https://api.anthropic.com",
        api_key_env="ANTHROPIC_API_KEY",
        default_model="claude-3-sonnet-20240229",
        models=[
            ModelInfo(
                name="claude-3-opus-20240229",
                display_name="Claude 3 Opus",
                description="Most powerful model for complex tasks",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_vision=True,
                    context_length=200000,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.015,
            ),
            ModelInfo(
                name="claude-3-sonnet-20240229",
                display_name="Claude 3 Sonnet",
                description="Balanced performance and speed",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_vision=True,
                    context_length=200000,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.003,
            ),
            ModelInfo(
                name="claude-3-haiku-20240307",
                display_name="Claude 3 Haiku",
                description="Fastest model for simple tasks",
                capabilities=ModelCapabilities(
                    supports_functions=True,
                    supports_streaming=True,
                    supports_vision=True,
                    context_length=200000,
                    max_tokens=4096,
                ),
                cost_per_1k_tokens=0.00025,
            ),
        ],
        requests_per_minute=1000,
        tokens_per_minute=100000,
    ),
    
    ProviderType.OLLAMA: ProviderConfig(
        name="ollama",
        type=ProviderType.OLLAMA,
        display_name="Ollama (Local)",
        base_url="http://localhost:11434",
        api_key_env="",  # No API key needed for local
        default_model="llama2",
        models=[
            ModelInfo(
                name="llama2",
                display_name="Llama 2",
                description="Meta's open source model",
                capabilities=ModelCapabilities(
                    supports_functions=False,
                    supports_streaming=True,
                    context_length=4096,
                    max_tokens=2048,
                ),
            ),
            ModelInfo(
                name="codellama",
                display_name="Code Llama",
                description="Specialized for code generation",
                capabilities=ModelCapabilities(
                    supports_functions=False,
                    supports_streaming=True,
                    supports_code=True,
                    context_length=16384,
                    max_tokens=4096,
                ),
            ),
            ModelInfo(
                name="mistral",
                display_name="Mistral",
                description="Efficient open source model",
                capabilities=ModelCapabilities(
                    supports_functions=False,
                    supports_streaming=True,
                    context_length=8192,
                    max_tokens=4096,
                ),
            ),
        ],
        timeout=120,  # Local models may be slower
    ),
}


def get_provider_config(provider_type: ProviderType) -> ProviderConfig:
    """Get configuration for a specific provider type."""
    return DEFAULT_PROVIDERS[provider_type]


def get_all_providers() -> Dict[ProviderType, ProviderConfig]:
    """Get all available provider configurations."""
    return DEFAULT_PROVIDERS.copy()


def get_provider_by_name(name: str) -> Optional[ProviderConfig]:
    """Get provider configuration by name."""
    for provider in DEFAULT_PROVIDERS.values():
        if provider.name == name:
            return provider
    return None


def get_model_info(provider_name: str, model_name: str) -> Optional[ModelInfo]:
    """Get information about a specific model."""
    provider = get_provider_by_name(provider_name)
    if not provider:
        return None
    
    for model in provider.models:
        if model.name == model_name:
            return model
    return None
