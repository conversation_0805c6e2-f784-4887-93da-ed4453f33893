#!/usr/bin/env python3
"""
AI Terminal Demo Script

Demonstrates the key features and capabilities of AI Terminal
including provider management, conversation handling, and tool execution.
"""

import asyncio
import os
from pathlib import Path

# Add the parent directory to the path so we can import ai_terminal
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from ai_terminal.config.settings import get_settings
from ai_terminal.providers.openai_client import OpenAIProvider
from ai_terminal.providers.ollama_client import OllamaProvider
from ai_terminal.agents.agent_loop import Agent<PERSON>oop
from ai_terminal.agents.tools import ToolRegistry
from ai_terminal.agents.context import ConversationContext
from ai_terminal.security.approval import ApprovalManager
from ai_terminal.storage.sessions import SessionManager
from ai_terminal.utils.logger import get_logger, setup_logging
from ai_terminal.utils.package_manager import PackageManager
from ai_terminal.utils.notifications import NotificationManager

# Setup logging
setup_logging(level="INFO", enable_console_logging=True, enable_file_logging=False)
logger = get_logger(__name__)


async def demo_configuration():
    """Demonstrate configuration management."""
    print("🔧 Configuration Management Demo")
    print("=" * 50)
    
    # Get settings
    settings = get_settings()
    
    print(f"App Name: {settings.app_name}")
    print(f"Version: {settings.version}")
    print(f"Default Provider: {settings.default_provider}")
    print(f"Config Directory: {settings.config_dir}")
    print(f"Data Directory: {settings.data_dir}")
    print(f"Debug Mode: {settings.debug}")
    print()


async def demo_providers():
    """Demonstrate provider management."""
    print("🤖 AI Provider Management Demo")
    print("=" * 50)
    
    # Test Ollama (local) provider
    print("Testing Ollama provider...")
    ollama = OllamaProvider()
    
    try:
        await ollama.initialize()
        is_available = await ollama.validate_connection()
        
        if is_available:
            print("✅ Ollama is available")
            models = await ollama.list_models()
            print(f"Available models: {models[:3]}...")  # Show first 3
        else:
            print("❌ Ollama is not running")
        
        await ollama.cleanup()
        
    except Exception as e:
        print(f"❌ Ollama error: {e}")
    
    # Test OpenAI provider (if API key is available)
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key:
        print("\nTesting OpenAI provider...")
        openai_provider = OpenAIProvider(api_key=openai_key)
        
        try:
            await openai_provider.initialize()
            is_available = await openai_provider.validate_connection()
            
            if is_available:
                print("✅ OpenAI is available")
                models = await openai_provider.list_models()
                print(f"Available models: {len(models)} total")
            else:
                print("❌ OpenAI connection failed")
            
            await openai_provider.cleanup()
            
        except Exception as e:
            print(f"❌ OpenAI error: {e}")
    else:
        print("\n⚠️  OpenAI API key not found (set OPENAI_API_KEY)")
    
    print()


async def demo_tools():
    """Demonstrate tool system."""
    print("🔧 Tool System Demo")
    print("=" * 50)
    
    # Create tool registry
    tools = ToolRegistry()
    
    print(f"Available tools: {len(tools.tools)}")
    for tool_name in tools.tools.keys():
        print(f"  - {tool_name}")
    
    print("\nTesting system info tool...")
    result = await tools.execute_tool("get_system_info", {})
    
    if result.success:
        print("✅ System info retrieved:")
        print(result.content[:200] + "..." if len(result.content) > 200 else result.content)
    else:
        print(f"❌ Tool failed: {result.content}")
    
    print("\nTesting file listing tool...")
    result = await tools.execute_tool("list_directory", {
        "directory_path": ".",
        "show_hidden": False
    })
    
    if result.success:
        print("✅ Directory listing:")
        lines = result.content.split('\n')
        for line in lines[:10]:  # Show first 10 lines
            print(f"  {line}")
        if len(lines) > 10:
            print(f"  ... and {len(lines) - 10} more items")
    else:
        print(f"❌ Directory listing failed: {result.content}")
    
    print()


async def demo_security():
    """Demonstrate security features."""
    print("🛡️ Security System Demo")
    print("=" * 50)
    
    # Create approval manager
    approval = ApprovalManager(interactive=False)  # Non-interactive for demo
    
    print("Testing command approval...")
    
    # Test safe command
    safe_approved = await approval.request_approval("execute_command", {"command": "echo hello"})
    print(f"Safe command 'echo hello': {'✅ Approved' if safe_approved else '❌ Denied'}")
    
    # Test dangerous command
    dangerous_approved = await approval.request_approval("execute_command", {"command": "rm -rf /"})
    print(f"Dangerous command 'rm -rf /': {'✅ Approved' if dangerous_approved else '❌ Denied'}")
    
    # Test file operations
    file_approved = await approval.request_approval("write_file", {"file_path": "test.txt"})
    print(f"File write 'test.txt': {'✅ Approved' if file_approved else '❌ Denied'}")
    
    system_file_approved = await approval.request_approval("write_file", {"file_path": "/etc/passwd"})
    print(f"System file write '/etc/passwd': {'✅ Approved' if system_file_approved else '❌ Denied'}")
    
    print()


async def demo_sessions():
    """Demonstrate session management."""
    print("💾 Session Management Demo")
    print("=" * 50)
    
    # Create session manager
    session_manager = SessionManager()
    
    # Create a sample conversation context
    context = ConversationContext(
        session_id="demo-session",
        system_message="You are a helpful AI assistant for demonstration purposes.",
        model="demo-model",
        temperature=0.7
    )
    
    # Add some sample messages
    from ai_terminal.providers.base import Message, MessageRole
    
    context.add_message(Message(
        role=MessageRole.USER,
        content="Hello, this is a demo conversation."
    ))
    
    context.add_message(Message(
        role=MessageRole.ASSISTANT,
        content="Hello! I'm here to help with your demo. What would you like to explore?"
    ))
    
    # Save session
    print("Saving demo session...")
    await session_manager.save_session(context)
    print("✅ Session saved")
    
    # List sessions
    sessions = await session_manager.list_sessions(limit=5)
    print(f"\nFound {len(sessions)} sessions:")
    for session in sessions:
        print(f"  - {session['session_id']}: {session['message_count']} messages")
    
    # Get session stats
    stats = await session_manager.get_session_stats()
    print(f"\nSession statistics:")
    print(f"  Total sessions: {stats.get('total_sessions', 0)}")
    print(f"  Total messages: {stats.get('total_messages', 0)}")
    
    print()


async def demo_package_manager():
    """Demonstrate package manager integration."""
    print("📦 Package Manager Demo")
    print("=" * 50)
    
    # Create package manager
    pkg_mgr = PackageManager()
    
    print("Detected package managers:")
    for name, info in pkg_mgr.managers.items():
        status = "✅ Available" if info.get("available") else "❌ Not found"
        print(f"  - {name}: {status}")
    
    print("\nChecking for updates...")
    try:
        update_info = await pkg_mgr.check_for_updates()
        
        if update_info.get("update_available"):
            print(f"🆕 Update available: {update_info['latest_version']}")
            print(f"   Current: {update_info['current_version']}")
        else:
            print("✅ AI Terminal is up to date")
            
    except Exception as e:
        print(f"❌ Update check failed: {e}")
    
    print("\nEnvironment info:")
    env_info = pkg_mgr.get_environment_info()
    print(f"  Python: {env_info.get('python_version')}")
    print(f"  Virtual env: {env_info.get('virtual_environment')}")
    print(f"  AI Terminal: {env_info.get('ai_terminal_version')}")
    
    print()


async def demo_notifications():
    """Demonstrate notification system."""
    print("🔔 Notification System Demo")
    print("=" * 50)
    
    # Create notification manager
    notifier = NotificationManager()
    
    print(f"Platform: {notifier.system}")
    print(f"Notifications supported: {'✅ Yes' if notifier.is_supported() else '❌ No'}")
    print(f"Notifications enabled: {'✅ Yes' if notifier.is_enabled() else '❌ No'}")
    
    if notifier.is_enabled():
        print("\nSending test notification...")
        success = await notifier.send_notification(
            title="AI Terminal Demo",
            message="This is a test notification from AI Terminal!",
            urgency="low"
        )
        
        if success:
            print("✅ Notification sent successfully")
        else:
            print("❌ Failed to send notification")
    
    print()


async def demo_conversation():
    """Demonstrate a simple conversation (if provider is available)."""
    print("💬 Conversation Demo")
    print("=" * 50)
    
    # Try to find an available provider
    provider = None
    
    # Try Ollama first
    try:
        ollama = OllamaProvider()
        await ollama.initialize()
        if await ollama.validate_connection():
            provider = ollama
            print("✅ Using Ollama for conversation demo")
        else:
            await ollama.cleanup()
    except Exception:
        pass
    
    # Try OpenAI if Ollama not available
    if not provider:
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            try:
                openai_provider = OpenAIProvider(api_key=openai_key)
                await openai_provider.initialize()
                if await openai_provider.validate_connection():
                    provider = openai_provider
                    print("✅ Using OpenAI for conversation demo")
                else:
                    await openai_provider.cleanup()
            except Exception:
                pass
    
    if provider:
        # Create agent loop
        tools = ToolRegistry()
        agent = AgentLoop(provider=provider, tool_registry=tools)
        
        # Start conversation
        context = await agent.start_conversation(
            system_message="You are a helpful AI assistant. Keep responses brief and friendly.",
            model=None,  # Use default
            temperature=0.7
        )
        
        print(f"Started conversation: {context.session_id[:8]}...")
        
        # Send a test message
        print("\nSending message: 'Hello! Can you tell me what time it is?'")
        
        response_content = ""
        async for chunk in agent.send_message(
            "Hello! Can you tell me what time it is?",
            stream=True
        ):
            if chunk.content:
                response_content += chunk.content
            
            if chunk.is_complete:
                break
        
        print(f"AI Response: {response_content}")
        
        # Cleanup
        await provider.cleanup()
        
    else:
        print("❌ No AI providers available for conversation demo")
        print("   To enable: set OPENAI_API_KEY or start Ollama")
    
    print()


async def main():
    """Run all demos."""
    print("🚀 AI Terminal Feature Demonstration")
    print("=" * 60)
    print()
    
    demos = [
        demo_configuration,
        demo_providers,
        demo_tools,
        demo_security,
        demo_sessions,
        demo_package_manager,
        demo_notifications,
        demo_conversation,
    ]
    
    for demo in demos:
        try:
            await demo()
        except Exception as e:
            print(f"❌ Demo failed: {e}")
            print()
    
    print("🎉 Demo completed!")
    print("\nTo try AI Terminal interactively:")
    print("  python cli.py setup    # Run setup wizard")
    print("  python cli.py chat     # Start interactive chat")
    print("  python cli.py quick 'Your message here'  # Send quick message")


if __name__ == "__main__":
    asyncio.run(main())
