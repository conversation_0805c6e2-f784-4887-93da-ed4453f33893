"""
Advanced logging system with async support and structured logging.

Provides platform-specific log paths, rotation, and performance monitoring.
"""

import asyncio
import logging
import logging.handlers
import platform
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import json
import queue
import threading
from contextlib import contextmanager

from ai_terminal.config.settings import get_settings


class AsyncQueueHandler(logging.Handler):
    """Async-safe logging handler using a queue."""
    
    def __init__(self, handler: logging.Handler):
        super().__init__()
        self.handler = handler
        self.queue = queue.Queue()
        self.thread = threading.Thread(target=self._worker, daemon=True)
        self.thread.start()
    
    def _worker(self):
        """Worker thread to process log records."""
        while True:
            try:
                record = self.queue.get(timeout=1)
                if record is None:
                    break
                self.handler.emit(record)
                self.queue.task_done()
            except queue.Empty:
                continue
            except Exception:
                # Avoid infinite recursion
                pass
    
    def emit(self, record):
        """Emit a log record to the queue."""
        try:
            self.queue.put_nowait(record)
        except queue.Full:
            # Drop the record if queue is full
            pass
    
    def close(self):
        """Close the handler and stop the worker thread."""
        self.queue.put(None)
        self.thread.join(timeout=5)
        self.handler.close()
        super().close()


class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for logs."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in {
                "name", "msg", "args", "levelname", "levelno", "pathname",
                "filename", "module", "lineno", "funcName", "created",
                "msecs", "relativeCreated", "thread", "threadName",
                "processName", "process", "getMessage", "exc_info",
                "exc_text", "stack_info"
            }:
                log_data[key] = value
        
        return json.dumps(log_data, default=str)


class ColoredConsoleFormatter(logging.Formatter):
    """Colored console formatter for better readability."""
    
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        color = self.COLORS.get(record.levelname, '')
        reset = self.RESET
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # Format message
        message = record.getMessage()
        
        # Add exception info if present
        if record.exc_info:
            message += '\n' + self.formatException(record.exc_info)
        
        return f"{color}[{timestamp}] {record.levelname:8} {record.name}: {message}{reset}"


def get_log_directory() -> Path:
    """Get platform-specific log directory."""
    settings = get_settings()
    log_dir = settings.data_dir / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    return log_dir


def setup_logging(
    level: str = "INFO",
    enable_file_logging: bool = True,
    enable_console_logging: bool = True,
    structured_logging: bool = False,
    max_file_size: int = 10_000_000,
    backup_count: int = 5,
) -> None:
    """Setup application logging.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        enable_file_logging: Enable logging to file
        enable_console_logging: Enable logging to console
        structured_logging: Use structured JSON logging for files
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
    """
    # Clear existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set log level
    log_level = getattr(logging, level.upper(), logging.INFO)
    root_logger.setLevel(log_level)
    
    handlers = []
    
    # Console handler
    if enable_console_logging:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        
        if platform.system() != "Windows":
            # Use colored output on Unix-like systems
            console_formatter = ColoredConsoleFormatter()
        else:
            # Simple format for Windows
            console_formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)-8s %(name)s: %(message)s',
                datefmt='%H:%M:%S'
            )
        
        console_handler.setFormatter(console_formatter)
        handlers.append(AsyncQueueHandler(console_handler))
    
    # File handler
    if enable_file_logging:
        log_dir = get_log_directory()
        log_file = log_dir / "ai-terminal.log"
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        
        if structured_logging:
            file_formatter = StructuredFormatter()
        else:
            file_formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)-8s %(name)s:%(lineno)d: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        file_handler.setFormatter(file_formatter)
        handlers.append(AsyncQueueHandler(file_handler))
    
    # Add handlers to root logger
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # Set specific logger levels
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("anthropic").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


@contextmanager
def log_performance(logger: logging.Logger, operation: str, **kwargs):
    """Context manager to log operation performance.
    
    Args:
        logger: Logger instance
        operation: Operation name
        **kwargs: Additional context to log
    """
    start_time = datetime.now()
    logger.debug(f"Starting {operation}", extra={"operation": operation, **kwargs})
    
    try:
        yield
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(
            f"Completed {operation} in {duration:.3f}s",
            extra={"operation": operation, "duration": duration, "status": "success", **kwargs}
        )
    except Exception as e:
        duration = (datetime.now() - start_time).total_seconds()
        logger.error(
            f"Failed {operation} after {duration:.3f}s: {e}",
            extra={"operation": operation, "duration": duration, "status": "error", "error": str(e), **kwargs},
            exc_info=True
        )
        raise


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")


# Initialize logging with default settings
def init_logging():
    """Initialize logging with settings from configuration."""
    settings = get_settings()
    
    setup_logging(
        level=settings.logging.level,
        enable_file_logging=settings.logging.file_enabled,
        enable_console_logging=settings.logging.console_enabled,
        structured_logging=False,  # Can be made configurable
        max_file_size=settings.logging.max_file_size,
        backup_count=settings.logging.backup_count,
    )


# Auto-initialize logging when module is imported
init_logging()
