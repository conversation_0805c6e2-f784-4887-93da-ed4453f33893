#!/usr/bin/env python3
"""
Installation and setup script for AI Terminal.

This script helps users install dependencies, configure the application,
and verify that everything is working correctly.
"""

import sys
import subprocess
import platform
import os
from pathlib import Path
import json


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def check_poetry():
    """Check if Poetry is available."""
    try:
        result = subprocess.run(["poetry", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Poetry is available: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Poetry not found")
    print("   Install Poetry from: https://python-poetry.org/docs/#installation")
    return False


def install_dependencies():
    """Install dependencies using Poetry."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Install dependencies
        result = subprocess.run(["poetry", "install"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def install_with_pip():
    """Install dependencies using pip as fallback."""
    print("\n📦 Installing dependencies with pip...")
    
    # Core dependencies
    dependencies = [
        "typer[all]>=0.9.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "rich>=13.7.0",
        "textual>=0.45.0",
        "openai>=1.6.0",
        "anthropic>=0.8.0",
        "aiohttp>=3.9.0",
        "aiofiles>=23.2.0",
        "PyYAML>=6.0.1",
        "watchdog>=3.0.0",
        "psutil>=5.9.0",
        "click>=8.1.0",
        "plyer>=2.1.0",
    ]
    
    # Platform-specific dependencies
    if platform.system() == "Windows":
        dependencies.append("win10toast>=0.9")
    
    try:
        for dep in dependencies:
            print(f"Installing {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
        
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_config_directory():
    """Create configuration directory."""
    print("\n📁 Creating configuration directory...")
    
    if platform.system() == "Windows":
        config_dir = Path(os.environ.get("APPDATA", "~")) / "ai-terminal"
    elif platform.system() == "Darwin":  # macOS
        config_dir = Path("~/Library/Application Support/ai-terminal")
    else:  # Linux
        config_dir = Path(os.environ.get("XDG_CONFIG_HOME", "~/.config")) / "ai-terminal"
    
    config_dir = config_dir.expanduser()
    config_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Configuration directory created: {config_dir}")
    return config_dir


def create_sample_config(config_dir):
    """Create a sample configuration file."""
    config_file = config_dir / "config.yaml"
    
    if config_file.exists():
        print("⚠️  Configuration file already exists, skipping")
        return
    
    sample_config = """# AI Terminal Configuration
# Copy this file and customize as needed

# Default provider and model
default_provider: "openai"
default_model: "gpt-4"

# Provider configurations
providers:
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    model: "gpt-4"
  
  deepseek:
    api_key: "${DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-coder"
  
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    model: "claude-3-sonnet-20240229"
  
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"

# Security settings
security:
  sandbox_enabled: true
  approval_required:
    - "rm"
    - "sudo"
    - "chmod"
    - "chown"
  max_file_size: 10000000  # 10MB

# UI settings
ui:
  theme: "dark"
  streaming: true
  syntax_highlighting: true
  compact_mode: false

# Logging settings
logging:
  level: "INFO"
  file_enabled: true
  console_enabled: true
"""
    
    try:
        with open(config_file, 'w') as f:
            f.write(sample_config)
        print(f"✅ Sample configuration created: {config_file}")
    except Exception as e:
        print(f"❌ Failed to create configuration: {e}")


def test_installation():
    """Test that the installation works."""
    print("\n🧪 Testing installation...")
    
    try:
        # Try to import the main module
        import ai_terminal
        print(f"✅ AI Terminal v{ai_terminal.__version__} imported successfully")
        
        # Test configuration
        from ai_terminal.config.settings import get_settings
        settings = get_settings()
        print("✅ Configuration system working")
        
        # Test tool registry
        from ai_terminal.agents.tools import ToolRegistry
        tools = ToolRegistry()
        print(f"✅ Tool registry loaded with {len(tools.tools)} tools")
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False


def show_next_steps():
    """Show next steps to the user."""
    print("\n🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Set up your AI provider API keys:")
    print("   export OPENAI_API_KEY='your-openai-key'")
    print("   export ANTHROPIC_API_KEY='your-anthropic-key'")
    print("   export DEEPSEEK_API_KEY='your-deepseek-key'")
    print("\n2. Run the setup wizard:")
    print("   python cli.py setup")
    print("\n3. Start chatting:")
    print("   python cli.py chat")
    print("\n4. Or send a quick message:")
    print("   python cli.py quick 'Hello, AI!'")
    print("\n📚 For more information, see the README.md file")


def main():
    """Main installation function."""
    print("🚀 AI Terminal Installation Script")
    print("=" * 40)
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    # Try Poetry first, then pip
    use_poetry = check_poetry()
    
    if use_poetry:
        if not install_dependencies():
            print("\n⚠️  Poetry installation failed, trying pip...")
            if not install_with_pip():
                sys.exit(1)
    else:
        if not install_with_pip():
            sys.exit(1)
    
    # Create configuration
    config_dir = create_config_directory()
    create_sample_config(config_dir)
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation verification failed")
        print("   Please check the error messages above and try again")
        sys.exit(1)
    
    # Show next steps
    show_next_steps()


if __name__ == "__main__":
    main()
